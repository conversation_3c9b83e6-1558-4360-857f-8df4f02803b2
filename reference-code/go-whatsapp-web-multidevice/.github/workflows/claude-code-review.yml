name: <PERSON> Review - Internal PRs Only

on:
  # Note: Only handles internal PRs due to Claude action limitations
  # Fork PRs don't have sufficient permissions to post comments
  pull_request:
    types: [opened, synchronize]
    paths:
      - "src/**/*.go"
      - "go.mod"
      - "go.sum"
      - "src/views/**/*.html"
      - "src/views/**/*.css"
      - "src/views/**/*.js"
      - "src/views/**/*.svg"

jobs:
  claude-review:
    # Only run for internal PRs to avoid permission issues
    if: github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      issues: write
      id-token: write
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Run Claude Code Review
        id: claude-review
        uses: anthropics/claude-code-action@beta
        with:
          claude_code_oauth_token: ${{ secrets.CLAUDE_CODE_OAUTH_TOKEN }}
          github_token: ${{ secrets.GITHUB_TOKEN }}

          # Optional: Specify model (defaults to Claude Sonnet 4, uncomment for Claude Opus 4)
          # model: "claude-opus-4-20250514"
          
          # Direct prompt for automated review (no @claude mention needed)
          direct_prompt: |
            Please review this Go application pull request and provide feedback on:
            
            **For Go code (.go files):**
            - Go idioms and best practices
            - Error handling patterns
            - Interface design and implementation
            - Memory management and potential leaks
            - Concurrency safety (goroutines, channels, mutexes)
            - Code organization and package structure
            - Performance considerations
            - Security vulnerabilities
            - Test coverage and quality
            - Proper use of Go standard library
            - Documentation and code comments
            
            **For Frontend code (HTML/CSS/JS files):**
            - JavaScript/Vue.js best practices and patterns
            - HTML semantics and accessibility
            - CSS performance and maintainability
            - Cross-browser compatibility
            - Frontend security (XSS, CSRF prevention)
            - UI/UX considerations
            - Code organization and modularity
            - Performance optimizations (DOM manipulation, asset loading)
            - Responsive design implementation
            - Vue.js component architecture and lifecycle management
            
            **General concerns:**
            - API integration between frontend and backend
            - Data validation on both client and server sides
            - Error handling consistency across the stack
            - Security best practices for the full application
            
            Be constructive and helpful in your feedback, focusing on language-specific concerns for each file type.

          # Optional: Use sticky comments to make Claude reuse the same comment on subsequent pushes to the same PR
          # use_sticky_comment: true
          
          # Optional: Add specific tools for running Go tests and linting, plus frontend tools
          allowed_tools: "Bash(cd src && go test ./...),Bash(cd src && go vet ./...),Bash(cd src && go fmt ./...),Bash(cd src && go mod tidy),Bash(find src/views -name '*.html' -exec htmlhint {} \\;),Bash(find src/views -name '*.css' -exec csslint {} \\;),Bash(find src/views -name '*.js' -exec jshint {} \\;)"
          
          # Optional: Skip review for certain conditions
          # if: |
          #   !contains(github.event.pull_request.title, '[skip-review]') &&
          #   !contains(github.event.pull_request.title, '[WIP]')
