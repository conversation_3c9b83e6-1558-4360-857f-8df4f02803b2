name: 🐞 Bug Report
description: Report a bug found in Application.
title: "🐞 "
labels: ["bug"]
assignees:
  - aldinokemal
body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to fill out this bug report!
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you see!
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: Reproduction Steps
      description: "How do you trigger this bug? Please walk us through it step by step."
      value: |
        1.
        2.
        3.
        ...
      render: bash
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: bash
  - type: textarea
    id: context
    attributes:
      label: Screenshots or Additional Context
      description: Add any screenshots or provide additional context that may be helpful in resolving the issue.
  - type: input
    id: version
    attributes:
      label: Application Version
      description: Select the version you are running.
      placeholder: 1.0.0
    validations:
      required: true
  - type: checkboxes
    id: verify
    attributes:
      label: Verification
      description: Please check closed issues and discussions before opening a new issue.
      options:
        - label: I have checked for existing closed issues and discussions.
          required: true
