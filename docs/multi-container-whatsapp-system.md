# 多容器WhatsApp管理系统设计文档

## 项目概述

基于 `go-whatsapp-web-multidevice` 项目构建的多容器管理系统，支持同时管理多个WhatsApp账号，每个账号运行在独立的容器中，配备独立的代理服务器。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web管理界面    │    │   API网关       │    │   监控系统      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    容器管理层                                      │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐ │
│  │   账号管理器     │    │   代理池管理     │    │   端口管理器     │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘ │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    容器实例层                                      │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐ │
│  │  WhatsApp-001   │    │  WhatsApp-002   │    │  WhatsApp-003   │ │
│  │  (Proxy-US-01)  │    │  (Proxy-EU-01)  │    │  (Proxy-AS-01)  │ │
│  │  Port: 3001     │    │  Port: 3002     │    │  Port: 3003     │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 核心功能模块

### 1. 容器编排管理层

#### 1.1 账号管理器 (AccountManager)

**功能职责：**
- 动态创建/删除WhatsApp容器实例
- 容器生命周期管理（启动、停止、重启、健康检查）
- 账号状态监控和管理
- 配置文件生成和管理

**核心接口：**
```go
type AccountManager interface {
    CreateAccount(accountID string, config *AccountConfig) error
    DeleteAccount(accountID string) error
    RestartAccount(accountID string) error
    GetAccountStatus(accountID string) (*AccountStatus, error)
    ListAccounts() ([]*AccountInfo, error)
    UpdateAccountConfig(accountID string, config *AccountConfig) error
}
```

#### 1.2 代理池管理器 (ProxyPool)

**功能职责：**
- 代理服务器的添加、删除、更新
- 代理健康检查和状态监控
- 智能代理分配策略
- 代理轮换和故障转移

**核心接口：**
```go
type ProxyPool interface {
    AllocateProxy(accountID string, requirements *ProxyRequirements) (*Proxy, error)
    ReleaseProxy(accountID string) error
    AddProxy(proxy *Proxy) error
    RemoveProxy(proxyID string) error
    CheckProxyHealth(proxyID string) (*ProxyCheckResult, error)
    RotateProxy(accountID string) error
}
```

#### 1.3 端口管理器 (PortPool)

**功能职责：**
- 端口分配和释放
- 端口冲突检测
- 端口使用统计

### 2. API网关和路由层

#### 2.1 请求路由

**路由规则：**
```
/api/v1/accounts/{accountID}/send/message  -> Container-{accountID}:port/send/message
/api/v1/accounts/{accountID}/user/info     -> Container-{accountID}:port/user/info
/api/v1/accounts/{accountID}/group/create  -> Container-{accountID}:port/group/create
```

#### 2.2 负载均衡

支持多种负载均衡策略：
- 轮询 (Round Robin)
- 最少连接 (Least Connections)
- 基于响应时间 (Response Time)

### 3. 数据隔离和持久化

#### 3.1 目录结构
```
data/
├── account-001/
│   ├── whatsapp.db          # WhatsApp会话数据
│   ├── chatstorage.db       # 聊天记录存储
│   ├── media/               # 媒体文件
│   ├── qrcode/              # 二维码文件
│   └── config.env           # 账号配置
├── account-002/
│   └── ...
├── shared/
│   ├── templates/           # 配置模板
│   ├── backups/             # 数据备份
│   └── logs/                # 系统日志
└── proxy-pool/
    ├── proxy-config.yml     # 代理配置
    └── proxy-stats.db       # 代理统计数据
```

#### 3.2 数据备份策略

- **增量备份**：每小时备份变更数据
- **全量备份**：每日备份完整数据
- **异地备份**：支持云存储备份

## 代理池管理详细设计

### 1. 代理数据结构

```go
type Proxy struct {
    ID              string        `json:"id" db:"id"`
    Name            string        `json:"name" db:"name"`
    Type            ProxyType     `json:"type" db:"type"`
    Host            string        `json:"host" db:"host"`
    Port            int           `json:"port" db:"port"`
    Username        string        `json:"username" db:"username"`
    Password        string        `json:"password" db:"password"`
    Country         string        `json:"country" db:"country"`
    Region          string        `json:"region" db:"region"`
    Status          ProxyStatus   `json:"status" db:"status"`
    LastCheck       time.Time     `json:"last_check" db:"last_check"`
    Latency         time.Duration `json:"latency" db:"latency"`
    SuccessRate     float64       `json:"success_rate" db:"success_rate"`
    InUse           bool          `json:"in_use" db:"in_use"`
    AssignedTo      string        `json:"assigned_to" db:"assigned_to"`
    MaxUsers        int           `json:"max_users" db:"max_users"`
    CurrentUsers    int           `json:"current_users" db:"current_users"`
    CreatedAt       time.Time     `json:"created_at" db:"created_at"`
    UpdatedAt       time.Time     `json:"updated_at" db:"updated_at"`
}
```

### 2. 代理分配策略

#### 2.1 轮询分配 (Round Robin)
按顺序循环分配代理，确保负载均衡。

#### 2.2 最少使用 (Least Used)
优先分配当前使用用户数最少的代理。

#### 2.3 基于延迟 (Latency Based)
优先分配延迟最低的代理。

#### 2.4 地理位置优化 (Geo Based)
根据目标地区选择最优代理。

### 3. 健康检查机制

#### 3.1 检查项目
- **连通性测试**：HTTP/HTTPS请求测试
- **延迟测试**：响应时间测量
- **IP检测**：验证代理IP地址
- **速度测试**：下载速度测试

#### 3.2 检查频率
- **正常代理**：每5分钟检查一次
- **故障代理**：每1分钟检查一次
- **新代理**：立即检查

#### 3.3 故障处理
- **自动切换**：代理故障时自动分配新代理
- **重试机制**：3次重试后标记为故障
- **通知告警**：故障时发送通知

### 4. 代理轮换机制

#### 4.1 轮换触发条件
- **定时轮换**：每小时轮换一次
- **性能轮换**：成功率低于80%时轮换
- **手动轮换**：管理员手动触发

#### 4.2 轮换策略
- **平滑切换**：新连接使用新代理，旧连接保持
- **强制切换**：立即切换所有连接
- **渐进切换**：逐步迁移连接

## 容器配置管理

### 1. 容器模板配置

```yaml
# docker-compose.template.yml
version: '3.8'
services:
  whatsapp-{ACCOUNT_ID}:
    image: aldinokemal2104/go-whatsapp-web-multidevice
    container_name: whatsapp-{ACCOUNT_ID}
    restart: unless-stopped
    ports:
      - "{PORT}:3000"
    volumes:
      - ./data/{ACCOUNT_ID}:/app/storages
    environment:
      - APP_PORT=3000
      - APP_DEBUG=true
      - APP_BASIC_AUTH={AUTH_CREDENTIALS}
      - WHATSAPP_WEBHOOK={WEBHOOK_URL}
      - HTTP_PROXY={PROXY_URL}
      - HTTPS_PROXY={PROXY_URL}
      - NO_PROXY=localhost,127.0.0.1
    command: ["rest"]
    networks:
      - whatsapp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/app/devices"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

networks:
  whatsapp-network:
    driver: bridge
```

### 2. 环境变量配置

```bash
# account-{ID}.env
APP_PORT=3000
APP_DEBUG=true
APP_OS=Chrome
APP_BASIC_AUTH=admin:password123
APP_BASE_PATH=/
DB_URI=file:storages/whatsapp.db?_foreign_keys=on
WHATSAPP_AUTO_REPLY=
WHATSAPP_AUTO_MARK_READ=false
WHATSAPP_WEBHOOK=http://webhook.example.com/whatsapp/{ACCOUNT_ID}
WHATSAPP_WEBHOOK_SECRET=secret123
WHATSAPP_ACCOUNT_VALIDATION=true
WHATSAPP_CHAT_STORAGE=true
HTTP_PROXY=http://username:<EMAIL>:8080
HTTPS_PROXY=http://username:<EMAIL>:8080
NO_PROXY=localhost,127.0.0.1,*.local
```

## 监控和日志系统

### 1. 监控指标

#### 1.1 系统级指标
- CPU使用率
- 内存使用率
- 磁盘使用率
- 网络流量

#### 1.2 应用级指标
- 容器运行状态
- 消息发送成功率
- API响应时间
- 错误率统计

#### 1.3 代理级指标
- 代理连通性
- 代理延迟
- 代理成功率
- 代理使用率

### 2. 日志管理

#### 2.1 日志分类
- **系统日志**：容器管理、代理管理
- **应用日志**：WhatsApp消息处理
- **访问日志**：API请求记录
- **错误日志**：异常和错误信息

#### 2.2 日志格式
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "INFO",
  "service": "account-manager",
  "account_id": "001",
  "proxy_id": "us-001",
  "message": "Account created successfully",
  "metadata": {
    "container_id": "abc123",
    "port": 3001,
    "proxy_latency": "120ms"
  }
}
```

### 3. 告警机制

#### 3.1 告警规则
- 容器异常退出
- 代理连接失败
- 消息发送失败率超过10%
- 系统资源使用率超过80%

#### 3.2 通知方式
- 邮件通知
- 短信通知
- Webhook通知
- 企业微信/钉钉通知

## 安全性设计

### 1. 网络安全

#### 1.1 容器网络隔离
- 每个容器运行在独立的网络命名空间
- 容器间通信通过内部网络
- 外部访问通过API网关统一入口

#### 1.2 代理安全
- 代理认证信息加密存储
- 支持HTTPS代理连接
- 代理连接超时和重试限制

### 2. 数据安全

#### 2.1 数据加密
- 敏感配置信息加密存储
- 数据库文件加密
- 备份文件加密

#### 2.2 访问控制
- 基于角色的访问控制(RBAC)
- API密钥认证
- 操作审计日志

### 3. 账号安全

#### 3.1 账号隔离
- 每个账号独立的数据目录
- 独立的数据库文件
- 独立的配置文件

#### 3.2 会话管理
- 会话超时机制
- 异常登录检测
- 多设备登录控制

## 部署和运维

### 1. 部署架构

#### 1.1 单机部署
适用于小规模使用，所有组件部署在单台服务器上。

#### 1.2 集群部署
适用于大规模使用，支持多台服务器负载均衡。

#### 1.3 云原生部署
支持Kubernetes部署，具备自动扩缩容能力。

### 2. 运维工具

#### 2.1 管理脚本
```bash
# 账号管理
./scripts/create-account.sh account-001
./scripts/delete-account.sh account-001
./scripts/restart-account.sh account-001

# 代理管理
./scripts/add-proxy.sh proxy-config.json
./scripts/test-proxy.sh proxy-001
./scripts/rotate-proxy.sh account-001

# 系统维护
./scripts/backup-data.sh
./scripts/cleanup-logs.sh
./scripts/health-check.sh
```

#### 2.2 监控面板
- Grafana仪表板
- 实时状态监控
- 历史数据分析
- 告警管理界面

### 3. 故障处理

#### 3.1 常见故障
- 容器启动失败
- 代理连接异常
- 数据库锁定
- 磁盘空间不足

#### 3.2 故障恢复
- 自动重启机制
- 数据恢复流程
- 代理自动切换
- 降级服务模式

## 性能优化

### 1. 容器优化

#### 1.1 资源限制
- CPU限制：每容器最大0.5核
- 内存限制：每容器最大512MB
- 磁盘IO限制：防止单容器占用过多IO

#### 1.2 镜像优化
- 使用多阶段构建减小镜像大小
- 缓存依赖包减少构建时间
- 定期清理无用镜像

### 2. 代理优化

#### 2.1 连接池
- 复用代理连接
- 连接预热机制
- 连接健康检查

#### 2.2 负载均衡
- 智能代理选择
- 动态负载调整
- 故障快速切换

### 3. 数据库优化

#### 3.1 SQLite优化
- WAL模式启用
- 索引优化
- 定期VACUUM

#### 3.2 备份优化
- 增量备份策略
- 压缩备份文件
- 异步备份处理

## 扩展性设计

### 1. 水平扩展

#### 1.1 多节点支持
- 支持多台服务器部署
- 节点间负载均衡
- 数据同步机制

#### 1.2 自动扩缩容
- 基于负载自动扩容
- 资源不足时自动缩容
- 预测性扩容

### 2. 功能扩展

#### 2.1 插件系统
- 支持自定义插件
- 插件热加载
- 插件管理界面

#### 2.2 API扩展
- RESTful API
- GraphQL支持
- Webhook集成

### 3. 集成扩展

#### 3.1 第三方集成
- CRM系统集成
- 营销平台集成
- 客服系统集成

#### 3.2 消息队列
- Redis队列支持
- RabbitMQ集成
- Kafka集成

## 开发指南

### 1. 项目结构

```
whatsapp-multi-container/
├── cmd/                    # 主程序入口
│   ├── manager/           # 管理器主程序
│   ├── gateway/           # API网关
│   └── monitor/           # 监控服务
├── internal/              # 内部包
│   ├── account/           # 账号管理
│   ├── proxy/             # 代理管理
│   ├── container/         # 容器管理
│   ├── gateway/           # 网关逻辑
│   └── monitor/           # 监控逻辑
├── pkg/                   # 公共包
│   ├── config/            # 配置管理
│   ├── logger/            # 日志管理
│   ├── database/          # 数据库操作
│   └── utils/             # 工具函数
├── web/                   # Web界面
│   ├── static/            # 静态资源
│   ├── templates/         # 模板文件
│   └── api/               # API接口
├── scripts/               # 运维脚本
├── configs/               # 配置文件
├── docs/                  # 文档
└── deployments/           # 部署文件
    ├── docker/            # Docker配置
    ├── kubernetes/        # K8s配置
    └── compose/           # Docker Compose
```

### 2. 开发环境搭建

#### 2.1 依赖安装
```bash
# 安装Go 1.21+
go version

# 安装Docker
docker --version

# 安装Docker Compose
docker-compose --version

# 克隆项目
git clone https://github.com/your-org/whatsapp-multi-container.git
cd whatsapp-multi-container

# 安装依赖
go mod download
```

#### 2.2 配置文件
```yaml
# configs/config.yml
server:
  host: "0.0.0.0"
  port: 8080
  
database:
  driver: "sqlite3"
  dsn: "data/manager.db"
  
proxy_pool:
  check_interval: "5m"
  rotation_interval: "1h"
  max_retries: 3
  
container:
  image: "aldinokemal2104/go-whatsapp-web-multidevice"
  network: "whatsapp-network"
  port_range: "3001-4000"
  
logging:
  level: "info"
  format: "json"
  output: "stdout"
```

### 3. API接口文档

#### 3.1 账号管理API

```http
# 创建账号
POST /api/v1/accounts
Content-Type: application/json

{
  "account_id": "test-001",
  "config": {
    "basic_auth": "admin:password",
    "webhook_url": "http://example.com/webhook",
    "auto_reply": "自动回复消息"
  },
  "proxy_requirements": {
    "country": "US",
    "max_latency": "5s",
    "min_success_rate": 0.8
  }
}

# 获取账号列表
GET /api/v1/accounts

# 获取账号详情
GET /api/v1/accounts/{account_id}

# 删除账号
DELETE /api/v1/accounts/{account_id}

# 重启账号
POST /api/v1/accounts/{account_id}/restart
```

#### 3.2 代理管理API

```http
# 添加代理
POST /api/v1/proxies
Content-Type: application/json

{
  "name": "US Proxy 1",
  "type": "http",
  "host": "proxy.example.com",
  "port": 8080,
  "username": "user",
  "password": "pass",
  "country": "US",
  "max_users": 1
}

# 获取代理列表
GET /api/v1/proxies

# 测试代理
POST /api/v1/proxies/{proxy_id}/test

# 删除代理
DELETE /api/v1/proxies/{proxy_id}
```

#### 3.3 监控API

```http
# 获取系统状态
GET /api/v1/status

# 获取账号状态
GET /api/v1/accounts/{account_id}/status

# 获取代理状态
GET /api/v1/proxies/{proxy_id}/status

# 获取统计数据
GET /api/v1/stats
```

### 4. 测试指南

#### 4.1 单元测试
```bash
# 运行所有测试
go test ./...

# 运行特定包测试
go test ./internal/proxy

# 运行测试并生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

#### 4.2 集成测试
```bash
# 启动测试环境
docker-compose -f deployments/compose/test.yml up -d

# 运行集成测试
go test -tags=integration ./tests/integration

# 清理测试环境
docker-compose -f deployments/compose/test.yml down
```

#### 4.3 性能测试
```bash
# 压力测试
go test -bench=. ./tests/benchmark

# 内存泄漏测试
go test -memprofile=mem.prof ./...
go tool pprof mem.prof
```

## 最佳实践

### 1. 代理使用建议

#### 1.1 代理选择
- 优先选择高质量的付费代理
- 避免使用免费代理服务
- 选择不同地区的代理分散风险
- 定期更换代理提供商

#### 1.2 代理配置
- 每个账号使用独立代理
- 设置合理的超时时间
- 启用代理认证增强安全性
- 监控代理使用情况

### 2. 账号管理建议

#### 2.1 账号创建
- 使用有意义的账号ID
- 设置强密码保护账号
- 配置合适的Webhook地址
- 定期备份账号数据

#### 2.2 账号维护
- 定期检查账号状态
- 及时处理异常账号
- 监控消息发送频率
- 避免频繁重启账号

### 3. 系统运维建议

#### 3.1 监控告警
- 设置关键指标告警
- 建立故障响应流程
- 定期检查系统健康状态
- 保持监控数据历史记录

#### 3.2 数据备份
- 制定备份策略
- 定期测试恢复流程
- 保持多份备份副本
- 异地存储重要数据

### 4. 安全防护建议

#### 4.1 网络安全
- 使用防火墙限制访问
- 启用HTTPS加密传输
- 定期更新系统补丁
- 监控异常网络活动

#### 4.2 数据安全
- 加密敏感数据存储
- 限制数据访问权限
- 定期审计操作日志
- 建立数据泄露应急预案

## 故障排除

### 1. 常见问题

#### 1.1 容器启动失败
**症状：** 容器无法正常启动
**原因：** 端口冲突、配置错误、资源不足
**解决：** 检查端口占用、验证配置文件、增加系统资源

#### 1.2 代理连接异常
**症状：** 代理无法连接或频繁断开
**原因：** 代理服务器故障、网络问题、认证失败
**解决：** 测试代理连通性、检查网络配置、验证认证信息

#### 1.3 消息发送失败
**症状：** WhatsApp消息无法发送
**原因：** 账号被封、网络问题、API限制
**解决：** 检查账号状态、更换代理、降低发送频率

### 2. 日志分析

#### 2.1 错误日志
```bash
# 查看容器日志
docker logs whatsapp-001

# 查看系统日志
tail -f /var/log/whatsapp-manager.log

# 搜索错误信息
grep "ERROR" /var/log/whatsapp-manager.log
```

#### 2.2 性能分析
```bash
# 查看容器资源使用
docker stats

# 查看系统资源
top
htop
iotop

# 查看网络连接
netstat -an | grep :3001
```

### 3. 恢复流程

#### 3.1 数据恢复
1. 停止相关服务
2. 从备份恢复数据
3. 验证数据完整性
4. 重启服务
5. 测试功能正常

#### 3.2 服务恢复
1. 识别故障范围
2. 隔离故障组件
3. 启用备用服务
4. 修复故障组件
5. 恢复正常服务

## 版本更新

### 1. 更新策略

#### 1.1 滚动更新
- 逐个更新容器实例
- 保持服务连续性
- 支持快速回滚

#### 1.2 蓝绿部署
- 部署新版本环境
- 切换流量到新环境
- 保留旧环境作为备份

### 2. 更新流程

#### 2.1 准备阶段
1. 备份当前数据
2. 测试新版本功能
3. 制定回滚计划
4. 通知相关人员

#### 2.2 执行阶段
1. 停止部分服务
2. 更新镜像版本
3. 启动新版本服务
4. 验证功能正常
5. 逐步切换流量

#### 2.3 验证阶段
1. 功能测试
2. 性能测试
3. 监控告警检查
4. 用户反馈收集

## 总结

本文档详细描述了多容器WhatsApp管理系统的设计和实现方案，涵盖了系统架构、核心功能、代理管理、安全性、部署运维等各个方面。

该系统具有以下特点：
- **高可用性**：支持多实例部署和故障自动恢复
- **高安全性**：完善的数据隔离和访问控制机制
- **高扩展性**：支持水平扩展和功能插件
- **易管理性**：提供完整的Web管理界面和API接口

通过合理的架构设计和最佳实践，该系统能够稳定、安全、高效地管理大量WhatsApp账号，满足企业级应用需求。
