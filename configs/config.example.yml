# WhatsApp多租户SaaS系统配置文件示例

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"

# 数据库配置
database:
  driver: "postgres"
  dsn: "******************************************/whatsapp_saas"
  max_open_conns: 50
  max_idle_conns: 10
  conn_max_lifetime: "1h"

# Redis配置
redis:
  url: "redis://redis:6379"
  max_retries: 3
  pool_size: 10

# 代理池配置
proxy_pool:
  check_interval: "5m"        # 代理健康检查间隔
  rotation_interval: "1h"     # 代理轮换间隔
  allocation_strategy: "least_used"  # 分配策略: round_robin, least_used, random
  max_retries: 3
  timeout: "10s"

# 容器配置
container:
  image: "aldinokemal2104/go-whatsapp-web-multidevice"
  port_range:
    start: 3001
    end: 4000
  resources:
    cpu_limit: "0.5"          # CPU限制
    memory_limit: "512M"      # 内存限制
  restart_policy: "unless-stopped"

# 消息同步配置
message_sync:
  interval: "3s"              # 消息同步间隔
  batch_size: 100             # 每次同步的消息数量
  max_history_hours: 24       # 最大同步历史时间
  retry_attempts: 3           # 失败重试次数
  timeout: "30s"              # 请求超时时间

# 聊天配置
chat:
  polling_interval: "3s"      # 前端轮询间隔
  message_retention_days: 365 # 消息保留天数
  max_sessions_per_agent: 10  # 每个客服最大会话数
  auto_assign: true           # 是否自动分配会话

# 群发配置
broadcast:
  max_concurrent_tasks: 5     # 最大并发群发任务数
  rate_limit_per_second: 10   # 每秒发送速率限制
  batch_size: 50              # 批量发送大小
  retry_attempts: 3           # 失败重试次数

# 日志配置
logging:
  level: "info"               # 日志级别: debug, info, warn, error
  format: "json"              # 日志格式: json, text
  output: "stdout"            # 输出: stdout, file
  file_path: "/var/log/whatsapp-saas/app.log"  # 文件路径（当output为file时）
  max_size: "100MB"           # 最大文件大小
  max_backups: 10             # 最大备份文件数
  max_age: 30                 # 最大保留天数

# 安全配置
security:
  jwt_secret: "your-jwt-secret-key-change-this-in-production"
  jwt_expiry: "24h"           # JWT过期时间
  api_keys:
    - "your-api-key-1"
    - "your-api-key-2"
  cors:
    allowed_origins:
      - "http://localhost:3000"
      - "https://yourdomain.com"
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
    allowed_headers:
      - "Content-Type"
      - "Authorization"

# 监控配置
monitoring:
  prometheus:
    enabled: true
    port: 9090
    path: "/metrics"
  health_check:
    enabled: true
    path: "/health"
    interval: "30s"

# 文件存储配置
storage:
  type: "local"               # 存储类型: local, s3, minio
  local:
    path: "/app/data/uploads"
  s3:
    bucket: "whatsapp-saas-files"
    region: "us-east-1"
    access_key: "your-access-key"
    secret_key: "your-secret-key"

# 通知配置
notifications:
  email:
    enabled: false
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your-app-password"
    from: "WhatsApp SaaS <<EMAIL>>"
  webhook:
    enabled: false
    url: "https://your-webhook-url.com/notifications"
    secret: "your-webhook-secret"

# 租户配置
tenant:
  default_plan: "basic"
  plans:
    basic:
      max_ports: 5
      max_agents: 10
      max_broadcasts_per_day: 100
    pro:
      max_ports: 20
      max_agents: 50
      max_broadcasts_per_day: 1000
    enterprise:
      max_ports: 100
      max_agents: 200
      max_broadcasts_per_day: 10000

# 开发环境配置
development:
  debug: true
  auto_migrate: true
  seed_data: true
  cors_allow_all: true

# 生产环境配置
production:
  debug: false
  auto_migrate: false
  seed_data: false
  cors_allow_all: false
  tls:
    enabled: true
    cert_file: "/etc/ssl/certs/server.crt"
    key_file: "/etc/ssl/private/server.key"
