# 多容器WhatsApp管理系统 - 代码实现

## 1. 项目结构

```
whatsapp-multi-container/
├── cmd/
│   ├── manager/main.go           # 主管理服务
│   ├── gateway/main.go           # API网关服务
│   └── monitor/main.go           # 监控服务
├── internal/
│   ├── account/                  # 账号管理模块
│   │   ├── manager.go
│   │   ├── types.go
│   │   └── service.go
│   ├── proxy/                    # 代理管理模块
│   │   ├── pool.go
│   │   ├── checker.go
│   │   ├── types.go
│   │   └── rotation.go
│   ├── container/                # 容器管理模块
│   │   ├── docker.go
│   │   ├── config.go
│   │   └── template.go
│   ├── gateway/                  # 网关模块
│   │   ├── router.go
│   │   ├── middleware.go
│   │   └── proxy.go
│   └── monitor/                  # 监控模块
│       ├── metrics.go
│       ├── health.go
│       └── alerts.go
├── pkg/
│   ├── config/config.go          # 配置管理
│   ├── logger/logger.go          # 日志管理
│   ├── database/db.go            # 数据库操作
│   └── utils/utils.go            # 工具函数
├── web/                          # Web界面
│   ├── static/                   # 静态资源
│   ├── templates/                # HTML模板
│   └── handlers/                 # HTTP处理器
├── scripts/                      # 运维脚本
├── configs/                      # 配置文件
└── deployments/                  # 部署文件
```

## 2. 核心数据结构

### 2.1 账号管理

```go
// internal/account/types.go
package account

import (
    "time"
)

type Account struct {
    ID          string    `json:"id" db:"id"`
    Name        string    `json:"name" db:"name"`
    ContainerID string    `json:"container_id" db:"container_id"`
    Port        int       `json:"port" db:"port"`
    ProxyID     string    `json:"proxy_id" db:"proxy_id"`
    Status      Status    `json:"status" db:"status"`
    Config      *Config   `json:"config"`
    CreatedAt   time.Time `json:"created_at" db:"created_at"`
    UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
    LastSeen    time.Time `json:"last_seen" db:"last_seen"`
}

type Status string

const (
    StatusCreating Status = "creating"
    StatusRunning  Status = "running"
    StatusStopped  Status = "stopped"
    StatusFailed   Status = "failed"
    StatusDeleting Status = "deleting"
)

type Config struct {
    BasicAuth     string            `json:"basic_auth"`
    WebhookURL    string            `json:"webhook_url"`
    AutoReply     string            `json:"auto_reply"`
    AutoMarkRead  bool              `json:"auto_mark_read"`
    Environment   map[string]string `json:"environment"`
}

type CreateRequest struct {
    AccountID         string                `json:"account_id"`
    Name              string                `json:"name"`
    Config            *Config               `json:"config"`
    ProxyRequirements *ProxyRequirements    `json:"proxy_requirements"`
}

type ProxyRequirements struct {
    Country        string        `json:"country"`
    Region         string        `json:"region"`
    MaxLatency     time.Duration `json:"max_latency"`
    MinSuccessRate float64       `json:"min_success_rate"`
    ProxyType      string        `json:"proxy_type"`
}
```

### 2.2 代理管理

```go
// internal/proxy/types.go
package proxy

import (
    "time"
)

type Proxy struct {
    ID              string        `json:"id" db:"id"`
    Name            string        `json:"name" db:"name"`
    Type            ProxyType     `json:"type" db:"type"`
    Host            string        `json:"host" db:"host"`
    Port            int           `json:"port" db:"port"`
    Username        string        `json:"username" db:"username"`
    Password        string        `json:"password" db:"password"`
    Country         string        `json:"country" db:"country"`
    Region          string        `json:"region" db:"region"`
    Status          ProxyStatus   `json:"status" db:"status"`
    LastCheck       time.Time     `json:"last_check" db:"last_check"`
    Latency         time.Duration `json:"latency" db:"latency"`
    SuccessRate     float64       `json:"success_rate" db:"success_rate"`
    InUse           bool          `json:"in_use" db:"in_use"`
    AssignedTo      string        `json:"assigned_to" db:"assigned_to"`
    MaxUsers        int           `json:"max_users" db:"max_users"`
    CurrentUsers    int           `json:"current_users" db:"current_users"`
    CreatedAt       time.Time     `json:"created_at" db:"created_at"`
    UpdatedAt       time.Time     `json:"updated_at" db:"updated_at"`
}

type ProxyType string

const (
    ProxyTypeHTTP   ProxyType = "http"
    ProxyTypeSOCKS5 ProxyType = "socks5"
)

type ProxyStatus string

const (
    ProxyStatusActive   ProxyStatus = "active"
    ProxyStatusInactive ProxyStatus = "inactive"
    ProxyStatusFailed   ProxyStatus = "failed"
    ProxyStatusTesting  ProxyStatus = "testing"
)

type AllocationStrategy string

const (
    StrategyRoundRobin AllocationStrategy = "round_robin"
    StrategyRandom     AllocationStrategy = "random"
    StrategyLeastUsed  AllocationStrategy = "least_used"
    StrategyGeoBased   AllocationStrategy = "geo_based"
    StrategyLatency    AllocationStrategy = "latency_based"
)

type ProxyCheckResult struct {
    Success bool          `json:"success"`
    Error   string        `json:"error,omitempty"`
    Latency time.Duration `json:"latency"`
    IP      string        `json:"ip,omitempty"`
}
```

## 3. 账号管理器实现

```go
// internal/account/manager.go
package account

import (
    "context"
    "database/sql"
    "fmt"
    "sync"
    "time"

    "github.com/docker/docker/client"
    "github.com/your-org/whatsapp-multi-container/internal/container"
    "github.com/your-org/whatsapp-multi-container/internal/proxy"
    "github.com/your-org/whatsapp-multi-container/pkg/logger"
)

type Manager struct {
    accounts    map[string]*Account
    docker      *client.Client
    proxyPool   *proxy.Pool
    portPool    *container.PortPool
    db          *sql.DB
    mu          sync.RWMutex
    log         logger.Logger
}

func NewManager(docker *client.Client, proxyPool *proxy.Pool, portPool *container.PortPool, db *sql.DB, log logger.Logger) *Manager {
    return &Manager{
        accounts:  make(map[string]*Account),
        docker:    docker,
        proxyPool: proxyPool,
        portPool:  portPool,
        db:        db,
        log:       log,
    }
}

func (m *Manager) CreateAccount(ctx context.Context, req *CreateRequest) (*Account, error) {
    m.mu.Lock()
    defer m.mu.Unlock()

    // 检查账号是否已存在
    if _, exists := m.accounts[req.AccountID]; exists {
        return nil, fmt.Errorf("account %s already exists", req.AccountID)
    }

    // 分配代理
    proxy, err := m.proxyPool.AllocateProxy(req.AccountID, req.ProxyRequirements)
    if err != nil {
        return nil, fmt.Errorf("failed to allocate proxy: %v", err)
    }

    // 分配端口
    port, err := m.portPool.Allocate()
    if err != nil {
        m.proxyPool.ReleaseProxy(req.AccountID)
        return nil, fmt.Errorf("failed to allocate port: %v", err)
    }

    // 创建账号对象
    account := &Account{
        ID:        req.AccountID,
        Name:      req.Name,
        Port:      port,
        ProxyID:   proxy.ID,
        Status:    StatusCreating,
        Config:    req.Config,
        CreatedAt: time.Now(),
        UpdatedAt: time.Now(),
    }

    // 创建容器
    containerID, err := m.createContainer(ctx, account, proxy)
    if err != nil {
        m.proxyPool.ReleaseProxy(req.AccountID)
        m.portPool.Release(port)
        return nil, fmt.Errorf("failed to create container: %v", err)
    }

    account.ContainerID = containerID
    account.Status = StatusRunning

    // 保存到数据库
    if err := m.saveAccount(account); err != nil {
        m.log.Error("Failed to save account to database", "error", err, "account_id", req.AccountID)
        // 继续执行，不回滚容器创建
    }

    // 添加到内存映射
    m.accounts[req.AccountID] = account

    m.log.Info("Account created successfully", "account_id", req.AccountID, "container_id", containerID, "port", port)
    return account, nil
}

func (m *Manager) DeleteAccount(ctx context.Context, accountID string) error {
    m.mu.Lock()
    defer m.mu.Unlock()

    account, exists := m.accounts[accountID]
    if !exists {
        return fmt.Errorf("account %s not found", accountID)
    }

    account.Status = StatusDeleting

    // 停止并删除容器
    if err := m.docker.ContainerStop(ctx, account.ContainerID, nil); err != nil {
        m.log.Warn("Failed to stop container", "error", err, "container_id", account.ContainerID)
    }

    if err := m.docker.ContainerRemove(ctx, account.ContainerID, types.ContainerRemoveOptions{Force: true}); err != nil {
        m.log.Warn("Failed to remove container", "error", err, "container_id", account.ContainerID)
    }

    // 释放资源
    m.proxyPool.ReleaseProxy(accountID)
    m.portPool.Release(account.Port)

    // 从数据库删除
    if err := m.deleteAccount(accountID); err != nil {
        m.log.Error("Failed to delete account from database", "error", err, "account_id", accountID)
    }

    // 从内存删除
    delete(m.accounts, accountID)

    m.log.Info("Account deleted successfully", "account_id", accountID)
    return nil
}

func (m *Manager) GetAccount(accountID string) (*Account, error) {
    m.mu.RLock()
    defer m.mu.RUnlock()

    account, exists := m.accounts[accountID]
    if !exists {
        return nil, fmt.Errorf("account %s not found", accountID)
    }

    return account, nil
}

func (m *Manager) ListAccounts() []*Account {
    m.mu.RLock()
    defer m.mu.RUnlock()

    accounts := make([]*Account, 0, len(m.accounts))
    for _, account := range m.accounts {
        accounts = append(accounts, account)
    }

    return accounts
}

func (m *Manager) RestartAccount(ctx context.Context, accountID string) error {
    m.mu.Lock()
    defer m.mu.Unlock()

    account, exists := m.accounts[accountID]
    if !exists {
        return fmt.Errorf("account %s not found", accountID)
    }

    // 重启容器
    if err := m.docker.ContainerRestart(ctx, account.ContainerID, nil); err != nil {
        return fmt.Errorf("failed to restart container: %v", err)
    }

    account.UpdatedAt = time.Now()
    m.log.Info("Account restarted successfully", "account_id", accountID)
    return nil
}

func (m *Manager) createContainer(ctx context.Context, account *Account, proxy *proxy.Proxy) (string, error) {
    containerManager := container.NewManager(m.docker, m.log)
    return containerManager.CreateWhatsAppContainer(ctx, account, proxy)
}

func (m *Manager) saveAccount(account *Account) error {
    query := `
        INSERT INTO accounts (id, name, container_id, port, proxy_id, status, config, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON CONFLICT(id) DO UPDATE SET
            name = excluded.name,
            container_id = excluded.container_id,
            port = excluded.port,
            proxy_id = excluded.proxy_id,
            status = excluded.status,
            config = excluded.config,
            updated_at = excluded.updated_at
    `

    configJSON, _ := json.Marshal(account.Config)
    _, err := m.db.Exec(query,
        account.ID,
        account.Name,
        account.ContainerID,
        account.Port,
        account.ProxyID,
        account.Status,
        string(configJSON),
        account.CreatedAt,
        account.UpdatedAt,
    )

    return err
}

func (m *Manager) deleteAccount(accountID string) error {
    query := "DELETE FROM accounts WHERE id = ?"
    _, err := m.db.Exec(query, accountID)
    return err
}

// 启动健康检查
func (m *Manager) StartHealthChecker(ctx context.Context) {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            m.checkAccountsHealth(ctx)
        }
    }
}

func (m *Manager) checkAccountsHealth(ctx context.Context) {
    m.mu.RLock()
    accounts := make([]*Account, 0, len(m.accounts))
    for _, account := range m.accounts {
        accounts = append(accounts, account)
    }
    m.mu.RUnlock()

    for _, account := range accounts {
        go m.checkSingleAccountHealth(ctx, account)
    }
}

func (m *Manager) checkSingleAccountHealth(ctx context.Context, account *Account) {
    // 检查容器状态
    containerJSON, err := m.docker.ContainerInspect(ctx, account.ContainerID)
    if err != nil {
        m.log.Error("Failed to inspect container", "error", err, "account_id", account.ID)
        return
    }

    m.mu.Lock()
    defer m.mu.Unlock()

    if containerJSON.State.Running {
        account.Status = StatusRunning
        account.LastSeen = time.Now()
    } else {
        account.Status = StatusStopped
        m.log.Warn("Container is not running", "account_id", account.ID, "container_id", account.ContainerID)
    }

    account.UpdatedAt = time.Now()
}
```

## 4. 代理池实现

```go
// internal/proxy/pool.go
package proxy

import (
    "context"
    "database/sql"
    "fmt"
    "math/rand"
    "sort"
    "sync"
    "time"

    "github.com/your-org/whatsapp-multi-container/pkg/logger"
)

type Pool struct {
    proxies     map[string]*Proxy
    assignments map[string]string // accountID -> proxyID
    strategy    AllocationStrategy
    checker     *Checker
    db          *sql.DB
    config      *PoolConfig
    mu          sync.RWMutex
    log         logger.Logger
    
    // 回调函数
    onProxyRotation func(accountID string, oldProxy, newProxy *Proxy)
}

type PoolConfig struct {
    CheckInterval     time.Duration `json:"check_interval"`
    MaxRetries        int           `json:"max_retries"`
    TimeoutDuration   time.Duration `json:"timeout_duration"`
    RotationInterval  time.Duration `json:"rotation_interval"`
    AllowSharedProxy  bool          `json:"allow_shared_proxy"`
    MaxUsersPerProxy  int           `json:"max_users_per_proxy"`
}

func NewPool(db *sql.DB, config *PoolConfig, log logger.Logger) *Pool {
    pool := &Pool{
        proxies:     make(map[string]*Proxy),
        assignments: make(map[string]string),
        strategy:    StrategyLeastUsed,
        db:          db,
        config:      config,
        log:         log,
        checker:     NewChecker(config.TimeoutDuration, log),
    }

    // 从数据库加载代理
    pool.loadProxiesFromDB()

    return pool
}

func (p *Pool) Start(ctx context.Context) {
    // 启动健康检查
    go p.startHealthChecker(ctx)
    
    // 启动代理轮换
    if p.config.RotationInterval > 0 {
        go p.startRotationScheduler(ctx)
    }
}

func (p *Pool) AllocateProxy(accountID string, requirements *ProxyRequirements) (*Proxy, error) {
    p.mu.Lock()
    defer p.mu.Unlock()

    // 检查是否已经分配了代理
    if proxyID, exists := p.assignments[accountID]; exists {
        if proxy, ok := p.proxies[proxyID]; ok && proxy.Status == ProxyStatusActive {
            return proxy, nil
        }
        // 清理无效分配
        delete(p.assignments, accountID)
    }

    // 根据策略选择代理
    proxy, err := p.selectProxy(requirements)
    if err != nil {
        return nil, err
    }

    // 分配代理
    proxy.InUse = true
    proxy.CurrentUsers++
    proxy.AssignedTo = accountID
    p.assignments[accountID] = proxy.ID

    // 更新数据库
    p.updateProxyInDB(proxy)

    p.log.Info("Proxy allocated", "account_id", accountID, "proxy_id", proxy.ID, "proxy_host", proxy.Host)
    return proxy, nil
}

func (p *Pool) ReleaseProxy(accountID string) error {
    p.mu.Lock()
    defer p.mu.Unlock()

    proxyID, exists := p.assignments[accountID]
    if !exists {
        return nil
    }

    proxy, ok := p.proxies[proxyID]
    if !ok {
        return fmt.Errorf("proxy not found: %s", proxyID)
    }

    proxy.CurrentUsers--
    if proxy.CurrentUsers <= 0 {
        proxy.InUse = false
        proxy.AssignedTo = ""
    }

    delete(p.assignments, accountID)
    p.updateProxyInDB(proxy)

    p.log.Info("Proxy released", "account_id", accountID, "proxy_id", proxy.ID)
    return nil
}

func (p *Pool) AddProxy(proxy *Proxy) error {
    p.mu.Lock()
    defer p.mu.Unlock()

    if _, exists := p.proxies[proxy.ID]; exists {
        return fmt.Errorf("proxy %s already exists", proxy.ID)
    }

    proxy.CreatedAt = time.Now()
    proxy.UpdatedAt = time.Now()
    proxy.Status = ProxyStatusTesting

    // 立即测试代理
    go func() {
        result := p.checker.CheckProxy(proxy)
        p.mu.Lock()
        defer p.mu.Unlock()
        
        if result.Success {
            proxy.Status = ProxyStatusActive
            proxy.SuccessRate = 1.0
        } else {
            proxy.Status = ProxyStatusFailed
            proxy.SuccessRate = 0.0
        }
        proxy.Latency = result.Latency
        proxy.LastCheck = time.Now()
        
        p.updateProxyInDB(proxy)
    }()

    p.proxies[proxy.ID] = proxy
    p.saveProxyToDB(proxy)

    p.log.Info("Proxy added", "proxy_id", proxy.ID, "proxy_host", proxy.Host)
    return nil
}

func (p *Pool) RemoveProxy(proxyID string) error {
    p.mu.Lock()
    defer p.mu.Unlock()

    proxy, exists := p.proxies[proxyID]
    if !exists {
        return fmt.Errorf("proxy %s not found", proxyID)
    }

    if proxy.InUse {
        return fmt.Errorf("proxy %s is in use, cannot remove", proxyID)
    }

    delete(p.proxies, proxyID)
    p.deleteProxyFromDB(proxyID)

    p.log.Info("Proxy removed", "proxy_id", proxyID)
    return nil
}

func (p *Pool) selectProxy(requirements *ProxyRequirements) (*Proxy, error) {
    availableProxies := p.getAvailableProxies(requirements)
    if len(availableProxies) == 0 {
        return nil, fmt.Errorf("no available proxies")
    }

    switch p.strategy {
    case StrategyRoundRobin:
        return p.selectRoundRobin(availableProxies), nil
    case StrategyRandom:
        return p.selectRandom(availableProxies), nil
    case StrategyLeastUsed:
        return p.selectLeastUsed(availableProxies), nil
    case StrategyLatency:
        return p.selectByLatency(availableProxies), nil
    case StrategyGeoBased:
        return p.selectByGeo(availableProxies, requirements), nil
    default:
        return p.selectLeastUsed(availableProxies), nil
    }
}

func (p *Pool) getAvailableProxies(requirements *ProxyRequirements) []*Proxy {
    var available []*Proxy

    for _, proxy := range p.proxies {
        if proxy.Status != ProxyStatusActive {
            continue
        }

        if !p.config.AllowSharedProxy && proxy.InUse {
            continue
        }

        if proxy.CurrentUsers >= proxy.MaxUsers {
            continue
        }

        if requirements != nil {
            if requirements.Country != "" && proxy.Country != requirements.Country {
                continue
            }
            if requirements.Region != "" && proxy.Region != requirements.Region {
                continue
            }
            if requirements.MaxLatency > 0 && proxy.Latency > requirements.MaxLatency {
                continue
            }
            if requirements.MinSuccessRate > 0 && proxy.SuccessRate < requirements.MinSuccessRate {
                continue
            }
            if requirements.ProxyType != "" && string(proxy.Type) != requirements.ProxyType {
                continue
            }
        }

        available = append(available, proxy)
    }

    return available
}

func (p *Pool) selectLeastUsed(proxies []*Proxy) *Proxy {
    if len(proxies) == 0 {
        return nil
    }

    sort.Slice(proxies, func(i, j int) bool {
        return proxies[i].CurrentUsers < proxies[j].CurrentUsers
    })

    return proxies[0]
}

func (p *Pool) selectByLatency(proxies []*Proxy) *Proxy {
    if len(proxies) == 0 {
        return nil
    }

    sort.Slice(proxies, func(i, j int) bool {
        return proxies[i].Latency < proxies[j].Latency
    })

    return proxies[0]
}

func (p *Pool) selectRandom(proxies []*Proxy) *Proxy {
    if len(proxies) == 0 {
        return nil
    }

    return proxies[rand.Intn(len(proxies))]
}

func (p *Pool) selectRoundRobin(proxies []*Proxy) *Proxy {
    // 简单的轮询实现，可以优化为更复杂的状态跟踪
    return p.selectRandom(proxies)
}

func (p *Pool) selectByGeo(proxies []*Proxy, requirements *ProxyRequirements) *Proxy {
    if requirements == nil || requirements.Country == "" {
        return p.selectLeastUsed(proxies)
    }

    // 优先选择指定国家的代理
    var countryProxies []*Proxy
    for _, proxy := range proxies {
        if proxy.Country == requirements.Country {
            countryProxies = append(countryProxies, proxy)
        }
    }

    if len(countryProxies) > 0 {
        return p.selectLeastUsed(countryProxies)
    }

    return p.selectLeastUsed(proxies)
}

func (p *Pool) startHealthChecker(ctx context.Context) {
    ticker := time.NewTicker(p.config.CheckInterval)
    defer ticker.Stop()

    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            p.checkAllProxies()
        }
    }
}

func (p *Pool) checkAllProxies() {
    p.mu.RLock()
    proxies := make([]*Proxy, 0, len(p.proxies))
    for _, proxy := range p.proxies {
        proxies = append(proxies, proxy)
    }
    p.mu.RUnlock()

    // 并发检查所有代理
    var wg sync.WaitGroup
    for _, proxy := range proxies {
        wg.Add(1)
        go func(pr *Proxy) {
            defer wg.Done()
            p.checkSingleProxy(pr)
        }(proxy)
    }
    wg.Wait()
}

func (p *Pool) checkSingleProxy(proxy *Proxy) {
    result := p.checker.CheckProxy(proxy)

    p.mu.Lock()
    defer p.mu.Unlock()

    proxy.LastCheck = time.Now()
    proxy.Latency = result.Latency

    if result.Success {
        if proxy.Status == ProxyStatusFailed {
            proxy.Status = ProxyStatusActive
            p.log.Info("Proxy recovered", "proxy_id", proxy.ID)
        }
        // 更新成功率 (指数移动平均)
        proxy.SuccessRate = proxy.SuccessRate*0.9 + 0.1
    } else {
        proxy.Status = ProxyStatusFailed
        proxy.SuccessRate = proxy.SuccessRate * 0.9
        p.log.Warn("Proxy check failed", "proxy_id", proxy.ID, "error", result.Error)

        // 如果代理失效，需要重新分配使用该代理的账号
        if proxy.InUse {
            p.handleProxyFailure(proxy)
        }
    }

    p.updateProxyInDB(proxy)
}

func (p *Pool) handleProxyFailure(proxy *Proxy) {
    // 通知账号管理器代理失效，需要重新分配
    if p.onProxyRotation != nil {
        // 尝试分配新代理
        newProxy, err := p.selectProxy(nil)
        if err != nil {
            p.log.Error("Failed to allocate new proxy for failed proxy", "proxy_id", proxy.ID, "error", err)
            return
        }

        p.onProxyRotation(proxy.AssignedTo, proxy, newProxy)
    }
}

// 数据库操作方法
func (p *Pool) loadProxiesFromDB() error {
    query := `
        SELECT id, name, type, host, port, username, password, country, region,
               status, last_check, latency, success_rate, in_use, assigned_to,
               max_users, current_users, created_at, updated_at
        FROM proxies
    `

    rows, err := p.db.Query(query)
    if err != nil {
        return err
    }
    defer rows.Close()

    for rows.Next() {
        proxy := &Proxy{}
        var latencyNs int64

        err := rows.Scan(
            &proxy.ID, &proxy.Name, &proxy.Type, &proxy.Host, &proxy.Port,
            &proxy.Username, &proxy.Password, &proxy.Country, &proxy.Region,
            &proxy.Status, &proxy.LastCheck, &latencyNs, &proxy.SuccessRate,
            &proxy.InUse, &proxy.AssignedTo, &proxy.MaxUsers, &proxy.CurrentUsers,
            &proxy.CreatedAt, &proxy.UpdatedAt,
        )
        if err != nil {
            p.log.Error("Failed to scan proxy row", "error", err)
            continue
        }

        proxy.Latency = time.Duration(latencyNs)
        p.proxies[proxy.ID] = proxy

        if proxy.AssignedTo != "" {
            p.assignments[proxy.AssignedTo] = proxy.ID
        }
    }

    p.log.Info("Loaded proxies from database", "count", len(p.proxies))
    return nil
}

func (p *Pool) saveProxyToDB(proxy *Proxy) error {
    query := `
        INSERT INTO proxies (
            id, name, type, host, port, username, password, country, region,
            status, last_check, latency, success_rate, in_use, assigned_to,
            max_users, current_users, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `

    _, err := p.db.Exec(query,
        proxy.ID, proxy.Name, proxy.Type, proxy.Host, proxy.Port,
        proxy.Username, proxy.Password, proxy.Country, proxy.Region,
        proxy.Status, proxy.LastCheck, int64(proxy.Latency), proxy.SuccessRate,
        proxy.InUse, proxy.AssignedTo, proxy.MaxUsers, proxy.CurrentUsers,
        proxy.CreatedAt, proxy.UpdatedAt,
    )

    return err
}

func (p *Pool) updateProxyInDB(proxy *Proxy) error {
    query := `
        UPDATE proxies SET
            name = ?, status = ?, last_check = ?, latency = ?, success_rate = ?,
            in_use = ?, assigned_to = ?, current_users = ?, updated_at = ?
        WHERE id = ?
    `

    _, err := p.db.Exec(query,
        proxy.Name, proxy.Status, proxy.LastCheck, int64(proxy.Latency),
        proxy.SuccessRate, proxy.InUse, proxy.AssignedTo, proxy.CurrentUsers,
        time.Now(), proxy.ID,
    )

    return err
}

func (p *Pool) deleteProxyFromDB(proxyID string) error {
    query := "DELETE FROM proxies WHERE id = ?"
    _, err := p.db.Exec(query, proxyID)
    return err
}
```

## 5. 容器管理实现

```go
// internal/container/docker.go
package container

import (
    "context"
    "fmt"
    "path/filepath"

    "github.com/docker/docker/api/types"
    "github.com/docker/docker/api/types/container"
    "github.com/docker/docker/client"
    "github.com/docker/go-connections/nat"
    
    "github.com/your-org/whatsapp-multi-container/internal/account"
    "github.com/your-org/whatsapp-multi-container/internal/proxy"
    "github.com/your-org/whatsapp-multi-container/pkg/logger"
)

type Manager struct {
    docker *client.Client
    log    logger.Logger
}

func NewManager(docker *client.Client, log logger.Logger) *Manager {
    return &Manager{
        docker: docker,
        log:    log,
    }
}

func (m *Manager) CreateWhatsAppContainer(ctx context.Context, account *account.Account, proxy *proxy.Proxy) (string, error) {
    // 构建代理URL
    proxyURL := m.buildProxyURL(proxy)
    
    // 创建数据目录
    dataDir := filepath.Join("data", account.ID)
    
    // 容器配置
    containerConfig := &container.Config{
        Image: "aldinokemal2104/go-whatsapp-web-multidevice",
        Env:   m.buildEnvironmentVariables(account, proxyURL),
        Cmd:   []string{"rest"},
        ExposedPorts: nat.PortSet{
            "3000/tcp": struct{}{},
        },
        Labels: map[string]string{
            "whatsapp.account.id":   account.ID,
            "whatsapp.account.name": account.Name,
            "whatsapp.proxy.id":     proxy.ID,
            "whatsapp.managed":      "true",
        },
        Healthcheck: &container.HealthConfig{
            Test:     []string{"CMD", "curl", "-f", "http://localhost:3000/app/devices"},
            Interval: 30 * time.Second,
            Timeout:  10 * time.Second,
            Retries:  3,
        },
    }
    
    hostConfig := &container.HostConfig{
        PortBindings: nat.PortMap{
            "3000/tcp": []nat.PortBinding{
                {
                    HostIP:   "0.0.0.0",
                    HostPort: fmt.Sprintf("%d", account.Port),
                },
            },
        },
        Binds: []string{
            fmt.Sprintf("%s:/app/storages", dataDir),
        },
        RestartPolicy: container.RestartPolicy{
            Name: "unless-stopped",
        },
        Resources: container.Resources{
            Memory:   512 * 1024 * 1024, // 512MB
            NanoCPUs: *********,          // 0.5 CPU
        },
    }
    
    networkConfig := &network.NetworkingConfig{
        EndpointsConfig: map[string]*network.EndpointSettings{
            "whatsapp-network": {},
        },
    }
    
    resp, err := m.docker.ContainerCreate(
        ctx,
        containerConfig,
        hostConfig,
        networkConfig,
        nil,
        fmt.Sprintf("whatsapp-%s", account.ID),
    )
    
    if err != nil {
        return "", fmt.Errorf("failed to create container: %v", err)
    }
    
    // 启动容器
    if err := m.docker.ContainerStart(ctx, resp.ID, types.ContainerStartOptions{}); err != nil {
        // 如果启动失败，清理容器
        m.docker.ContainerRemove(ctx, resp.ID, types.ContainerRemoveOptions{Force: true})
        return "", fmt.Errorf("failed to start container: %v", err)
    }
    
    m.log.Info("Container created and started", 
        "container_id", resp.ID, 
        "account_id", account.ID,
        "port", account.Port,
        "proxy_id", proxy.ID)
    
    return resp.ID, nil
}

func (m *Manager) buildProxyURL(proxy *proxy.Proxy) string {
    if proxy.Username != "" {
        return fmt.Sprintf("%s://%s:%s@%s:%d", 
            proxy.Type, proxy.Username, proxy.Password, proxy.Host, proxy.Port)
    }
    return fmt.Sprintf("%s://%s:%d", proxy.Type, proxy.Host, proxy.Port)
}

func (m *Manager) buildEnvironmentVariables(account *account.Account, proxyURL string) []string {
    env := []string{
        "APP_PORT=3000",
        "APP_DEBUG=true",
        "APP_OS=Chrome",
        fmt.Sprintf("HTTP_PROXY=%s", proxyURL),
        fmt.Sprintf("HTTPS_PROXY=%s", proxyURL),
        "NO_PROXY=localhost,127.0.0.1",
        "WHATSAPP_ACCOUNT_VALIDATION=true",
        "WHATSAPP_CHAT_STORAGE=true",
    }
    
    if account.Config != nil {
        if account.Config.BasicAuth != "" {
            env = append(env, fmt.Sprintf("APP_BASIC_AUTH=%s", account.Config.BasicAuth))
        }
        if account.Config.WebhookURL != "" {
            env = append(env, fmt.Sprintf("WHATSAPP_WEBHOOK=%s", account.Config.WebhookURL))
        }
        if account.Config.AutoReply != "" {
            env = append(env, fmt.Sprintf("WHATSAPP_AUTO_REPLY=%s", account.Config.AutoReply))
        }
        if account.Config.AutoMarkRead {
            env = append(env, "WHATSAPP_AUTO_MARK_READ=true")
        }
        
        // 添加自定义环境变量
        for key, value := range account.Config.Environment {
            env = append(env, fmt.Sprintf("%s=%s", key, value))
        }
    }
    
    return env
}

// 端口池管理
type PortPool struct {
    startPort int
    endPort   int
    used      map[int]bool
    mu        sync.Mutex
}

func NewPortPool(startPort, endPort int) *PortPool {
    return &PortPool{
        startPort: startPort,
        endPort:   endPort,
        used:      make(map[int]bool),
    }
}

func (pp *PortPool) Allocate() (int, error) {
    pp.mu.Lock()
    defer pp.mu.Unlock()
    
    for port := pp.startPort; port <= pp.endPort; port++ {
        if !pp.used[port] {
            pp.used[port] = true
            return port, nil
        }
    }
    
    return 0, fmt.Errorf("no available ports in range %d-%d", pp.startPort, pp.endPort)
}

func (pp *PortPool) Release(port int) {
    pp.mu.Lock()
    defer pp.mu.Unlock()
    
    delete(pp.used, port)
}

func (pp *PortPool) IsUsed(port int) bool {
    pp.mu.Lock()
    defer pp.mu.Unlock()
    
    return pp.used[port]
}
```

这个代码实现提供了：

1. **完整的账号管理器**：支持创建、删除、重启账号，包含健康检查
2. **智能代理池**：多种分配策略，自动健康检查，故障转移
3. **容器管理器**：Docker容器的创建和配置，包含代理设置
4. **端口池管理**：自动分配和释放端口
5. **数据库持久化**：所有状态信息的持久化存储
6. **并发安全**：使用互斥锁保证并发安全
7. **日志记录**：详细的操作日志记录

这些核心组件可以组合成完整的多容器WhatsApp管理系统。
