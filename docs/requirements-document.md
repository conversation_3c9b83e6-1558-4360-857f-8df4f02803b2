# WhatsApp多租户SaaS系统 - 需求文档

## 1. 项目概述

### 1.1 项目背景
构建一个基于WhatsApp的多租户SaaS客服系统，为企业提供专业的WhatsApp客服解决方案。系统支持多租户隔离，每个租户可以管理多个WhatsApp账号，提供客服聊天、群发营销等功能。

### 1.2 项目目标
- 提供稳定可靠的WhatsApp客服服务
- 支持多租户架构，数据完全隔离
- 提供直观易用的管理界面
- 支持大规模并发聊天处理
- 提供营销推广功能

### 1.3 技术栈选择
- **后端**: Rust + Axum + Tokio
- **前端**: Vue.js 3 + TypeScript
- **数据库**: PostgreSQL + Redis
- **容器**: Docker
- **部署**: 单服务器部署

## 2. 功能需求

### 2.1 用户角色定义

#### 2.1.1 系统管理员 (System Admin)
**职责**: 管理整个SaaS平台
**权限**:
- 创建、编辑、删除租户
- 管理租户套餐和配额
- 管理代理池
- 查看系统整体运营数据
- 可以切换到任意租户进行操作
- 系统配置和维护

#### 2.1.2 租户管理员 (Tenant Admin)
**职责**: 管理租户内的所有资源
**权限**:
- 创建、管理WhatsApp账号
- 创建、管理客服人员
- 设置群发任务
- 查看租户内聊天数据和统计
- 可以切换到客服界面进行聊天
- 分配WhatsApp账号给客服

#### 2.1.3 客服人员 (Customer Service)
**职责**: 处理客户聊天
**权限**:
- 接收和回复客户消息
- 查看分配给自己的聊天会话
- 查看客户聊天历史
- 使用快捷回复模板

### 2.2 核心功能模块

#### 2.2.1 租户管理模块
**功能描述**: 管理SaaS平台的租户
**主要功能**:
- 租户注册和激活
- 套餐管理 (Basic/Pro/Enterprise)
- 端口配额分配和管理
- 租户状态管理 (活跃/暂停/删除)
- 计费和续费管理

**业务规则**:
- 每个租户有独立的端口配额
- 端口数量决定可创建的WhatsApp账号数量
- 租户数据完全隔离
- 支持套餐升级/降级

#### 2.2.2 WhatsApp账号管理模块
**功能描述**: 管理租户的WhatsApp账号
**主要功能**:
- 创建WhatsApp账号容器
- 扫码登录WhatsApp
- 账号状态监控
- 代理分配和管理
- 账号分配给客服

**业务规则**:
- 每个账号运行在独立容器中
- 每个账号使用独立代理
- 账号数量不能超过租户配额
- 支持账号的启动/停止/重启

#### 2.2.3 客服聊天模块
**功能描述**: 提供实时客服聊天功能
**主要功能**:
- 实时接收客户消息
- 发送文本、图片、文件消息
- 聊天会话管理
- 客户信息管理
- 聊天历史查询
- 快捷回复模板

**业务规则**:
- 支持多客服同时在线
- 聊天会话可以在客服间转移
- 消息定时同步 (3秒轮询)
- 聊天记录永久保存

#### 2.2.4 群发营销模块
**功能描述**: 批量发送营销消息
**主要功能**:
- 创建群发任务
- 消息模板管理
- 目标群体管理
- 定时发送
- 发送状态跟踪
- 效果统计分析

**业务规则**:
- 支持立即发送和定时发送
- 可以选择多个WhatsApp账号发送
- 支持消息模板变量替换
- 发送频率限制防止封号

#### 2.2.5 代理池管理模块
**功能描述**: 管理代理服务器池
**主要功能**:
- 添加/删除代理服务器
- 代理健康检查
- 代理性能监控
- 智能代理分配
- 代理轮换策略

**业务规则**:
- 每个WhatsApp账号使用独立代理
- 代理故障时自动切换
- 支持多种代理类型 (HTTP/SOCKS5)
- 代理按地理位置分配

### 2.3 非功能性需求

#### 2.3.1 性能需求
- 支持1000+并发聊天会话
- 消息同步延迟 < 3s
- 系统响应时间 < 2s
- 支持10000+历史消息查询

#### 2.3.2 可用性需求
- 系统可用性 99.5%
- 容器故障自动恢复
- 代理故障自动切换
- 数据备份和恢复

#### 2.3.3 安全需求
- 租户数据完全隔离
- API接口认证和授权
- 敏感数据加密存储
- 操作日志审计

#### 2.3.4 扩展性需求
- 支持水平扩展
- 模块化架构设计
- 插件化功能扩展
- API接口标准化

## 3. 用户界面需求

### 3.1 系统管理员界面

#### 3.1.1 租户管理页面
**页面功能**:
- 租户列表展示
- 创建新租户
- 编辑租户信息
- 查看租户使用统计
- 租户状态管理

**界面元素**:
- 租户列表表格
- 搜索和筛选功能
- 创建租户表单
- 统计图表
- 操作按钮

#### 3.1.2 代理管理页面
**页面功能**:
- 代理列表展示
- 添加新代理
- 代理状态监控
- 代理性能统计
- 代理测试功能

**界面元素**:
- 代理列表表格
- 代理状态指示器
- 性能图表
- 添加代理表单
- 测试按钮

#### 3.1.3 系统监控页面
**页面功能**:
- 系统整体状态
- 资源使用情况
- 错误日志查看
- 性能指标监控

**界面元素**:
- 状态仪表板
- 资源使用图表
- 日志列表
- 告警信息

### 3.2 租户管理员界面

#### 3.2.1 仪表板页面
**页面功能**:
- 租户概览信息
- WhatsApp账号状态
- 聊天统计数据
- 最近活动记录

**界面元素**:
- 统计卡片
- 状态图表
- 活动时间线
- 快捷操作按钮

#### 3.2.2 WhatsApp账号管理页面
**页面功能**:
- 账号列表展示
- 创建新账号
- 账号状态管理
- 扫码登录
- 账号分配

**界面元素**:
- 账号列表表格
- 创建账号表单
- 二维码显示
- 状态指示器
- 分配下拉框

#### 3.2.3 客服管理页面
**页面功能**:
- 客服列表展示
- 创建客服账号
- 客服状态监控
- 权限设置

**界面元素**:
- 客服列表表格
- 创建客服表单
- 在线状态指示
- 权限设置面板

#### 3.2.4 群发管理页面
**页面功能**:
- 群发任务列表
- 创建群发任务
- 消息模板管理
- 发送统计

**界面元素**:
- 任务列表表格
- 创建任务表单
- 模板编辑器
- 统计图表

#### 3.2.5 聊天监控页面
**页面功能**:
- 实时聊天监控
- 客服工作状态
- 聊天分配管理
- 服务质量统计

**界面元素**:
- 聊天列表
- 客服状态面板
- 分配操作按钮
- 统计图表

### 3.3 客服人员界面

#### 3.3.1 聊天工作台
**页面功能**:
- 聊天会话列表
- 实时消息接收
- 消息发送
- 客户信息查看
- 聊天历史查询

**界面元素**:
- 会话列表侧边栏
- 聊天消息区域
- 消息输入框
- 客户信息面板
- 快捷回复按钮

**交互要求**:
- 定时消息刷新（3秒间隔）
- 消息状态显示
- 文件拖拽上传
- 表情符号支持
- 消息搜索功能
- 手动刷新按钮

## 4. 数据需求

### 4.1 数据模型

#### 4.1.1 租户相关数据
```
租户 (Tenant)
- ID: 唯一标识
- 名称: 租户名称
- 套餐: 订阅套餐类型
- 端口配额: 可用端口数量
- 已用端口: 已使用端口数量
- 状态: 租户状态
- 创建时间: 创建时间
- 到期时间: 套餐到期时间
```

#### 4.1.2 用户相关数据
```
用户 (User)
- ID: 唯一标识
- 租户ID: 所属租户
- 用户名: 登录用户名
- 邮箱: 邮箱地址
- 密码哈希: 加密后的密码
- 角色: 用户角色
- 状态: 用户状态
- 最后登录: 最后登录时间
- 创建时间: 创建时间
```

#### 4.1.3 WhatsApp账号数据
```
WhatsApp账号 (WhatsAppAccount)
- ID: 唯一标识
- 租户ID: 所属租户
- 名称: 账号名称
- 手机号: WhatsApp手机号
- 容器ID: Docker容器ID
- 端口: 分配的端口
- 代理ID: 使用的代理
- 状态: 账号状态
- 分配给: 分配的客服ID
- 创建时间: 创建时间
```

#### 4.1.4 聊天相关数据
```
聊天会话 (ChatSession)
- ID: 唯一标识
- WhatsApp账号ID: 使用的账号
- 客户手机号: 客户手机号
- 客户姓名: 客户姓名
- 分配客服: 处理的客服ID
- 状态: 会话状态
- 创建时间: 创建时间
- 更新时间: 最后更新时间

消息 (Message)
- ID: 唯一标识
- 会话ID: 所属会话
- 发送者ID: 发送者用户ID
- 内容: 消息内容
- 消息类型: 文本/图片/文件等
- 方向: 入站/出站
- WhatsApp消息ID: 原始消息ID
- 创建时间: 发送时间
```

#### 4.1.5 群发相关数据
```
群发任务 (BroadcastTask)
- ID: 唯一标识
- 租户ID: 所属租户
- 任务名称: 任务名称
- 消息模板: 消息内容模板
- 目标群体: 发送目标列表
- 计划时间: 计划发送时间
- 状态: 任务状态
- 创建者: 创建用户ID
- 创建时间: 创建时间

群发记录 (BroadcastRecord)
- ID: 唯一标识
- 任务ID: 群发任务ID
- 目标手机号: 接收者手机号
- 发送状态: 发送状态
- 发送时间: 实际发送时间
- 错误信息: 失败原因
```

### 4.2 数据存储需求

#### 4.2.1 数据库选择
- **主数据库**: PostgreSQL
  - 支持复杂查询和事务
  - 良好的并发性能
  - 丰富的数据类型支持

- **缓存数据库**: Redis
  - 会话存储
  - 实时数据缓存
  - 消息队列

#### 4.2.2 数据备份策略
- 每日全量备份
- 每小时增量备份
- 异地备份存储
- 备份数据加密

#### 4.2.3 数据保留策略
- 聊天记录: 永久保存
- 系统日志: 保留6个月
- 操作审计: 保留1年
- 临时文件: 保留7天

## 5. 集成需求

### 5.1 WhatsApp集成
- 基于 go-whatsapp-web-multidevice
- 支持多设备登录
- 消息实时接收和发送
- 媒体文件处理

### 5.2 代理服务集成
- 支持HTTP/SOCKS5代理
- 代理健康检查
- 自动故障切换
- 地理位置路由

### 5.3 容器管理集成
- Docker API集成
- 容器生命周期管理
- 资源限制和监控
- 网络隔离

### 5.4 监控集成
- Prometheus指标收集
- Grafana可视化
- 告警通知
- 日志聚合

## 6. 部署需求

### 6.1 硬件需求
- **CPU**: 8核心以上
- **内存**: 16GB以上
- **存储**: 500GB SSD
- **网络**: 100Mbps带宽

### 6.2 软件需求
- **操作系统**: Ubuntu 20.04 LTS
- **容器**: Docker 20.10+
- **数据库**: PostgreSQL 15+
- **缓存**: Redis 7+

### 6.3 部署架构
- 单服务器部署
- Docker Compose编排
- Nginx反向代理
- SSL证书配置

## 7. 项目计划

### 7.1 开发阶段

#### 阶段一: 基础架构 (4周)
- 项目框架搭建
- 数据库设计和实现
- 用户认证系统
- 基础API接口

#### 阶段二: 核心功能 (6周)
- 租户管理功能
- WhatsApp账号管理
- 容器管理系统
- 代理池管理

#### 阶段三: 聊天功能 (4周)
- 实时聊天系统
- 消息处理
- WebSocket集成
- 客服工作台

#### 阶段四: 营销功能 (3周)
- 群发任务系统
- 消息模板管理
- 定时任务调度
- 统计分析

#### 阶段五: 前端开发 (5周)
- Vue.js应用开发
- 管理界面实现
- 客服工作台
- 响应式设计

#### 阶段六: 测试部署 (2周)
- 功能测试
- 性能测试
- 部署配置
- 文档编写

### 7.2 里程碑
- M1: 基础架构完成
- M2: 核心功能完成
- M3: 聊天功能完成
- M4: 营销功能完成
- M5: 前端开发完成
- M6: 系统上线

## 8. 风险评估

### 8.1 技术风险
- WhatsApp API变更风险
- 代理服务稳定性风险
- 大并发性能风险
- 数据安全风险

### 8.2 业务风险
- 市场竞争风险
- 合规性风险
- 客户需求变更风险
- 运营成本风险

### 8.3 风险缓解措施
- 技术方案备选
- 性能压力测试
- 安全审计
- 业务需求锁定
