# WhatsApp多租户SaaS客服系统

基于Rust和Vue.js构建的企业级多租户WhatsApp客服SaaS系统，为企业提供专业的WhatsApp客服解决方案。系统支持多租户隔离，每个租户可以管理多个WhatsApp账号，提供实时客服聊天、群发营销等功能。

## 🚀 核心特性

### 👥 多租户架构
- **数据隔离**：租户间数据完全隔离，确保安全性
- **资源配额**：基于套餐的端口配额管理
- **权限控制**：细粒度的角色权限管理
- **租户切换**：管理员可以无缝切换到任意租户

### 📱 WhatsApp账号管理
- **独立容器**：每个WhatsApp账号运行在独立的Docker容器中
- **动态创建**：支持运行时动态创建和删除账号
- **状态监控**：实时监控每个账号的运行状态
- **自动恢复**：容器异常时自动重启和故障转移

### 💬 实时客服聊天
- **多客服支持**：支持多个客服同时在线处理聊天
- **实时通信**：基于WebSocket的实时消息推送
- **会话管理**：智能的聊天会话分配和转移
- **聊天历史**：完整的聊天记录存储和查询

### 📢 群发营销功能
- **批量发送**：支持大规模群发消息
- **定时发送**：支持定时群发任务
- **模板管理**：消息模板和变量替换
- **效果统计**：详细的发送统计和分析

### 🌐 智能代理池
- **代理分配**：智能分配代理服务器，避免IP封禁风险
- **健康检查**：自动检测代理连通性和性能
- **故障转移**：代理失效时自动切换到备用代理
- **负载均衡**：支持多种分配策略（轮询、最少使用、延迟优先等）

### 🔧 管理功能
- **Web界面**：直观的多角色管理界面
- **API接口**：完整的RESTful API
- **权限降级**：管理员可以降级到客服界面
- **配置管理**：灵活的配置模板和环境变量

### 📊 监控告警
- **实时监控**：Prometheus + Grafana监控面板
- **性能指标**：CPU、内存、网络、消息统计
- **告警通知**：邮件、短信、Webhook通知
- **日志聚合**：结构化日志收集和分析

### 🔒 安全防护
- **数据隔离**：租户间数据完全隔离
- **访问控制**：基于JWT的认证和授权
- **网络安全**：容器网络隔离和防火墙
- **数据加密**：敏感数据加密存储

## 📋 系统要求

### 硬件要求
- **CPU**: 8核心以上
- **内存**: 16GB以上
- **存储**: 500GB以上SSD
- **网络**: 100Mbps带宽

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+)
- **Docker**: 24.0+
- **Docker Compose**: 2.0+
- **Rust**: 1.75+ (开发环境)
- **Node.js**: 18+ (前端开发环境)

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Vue.js)                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   系统管理台     │ │   租户管理台     │ │   客服工作台     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │ HTTP/WebSocket
┌─────────────────────────────────────────────────────────────┐
│                  API网关层 (Nginx)                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   负载均衡       │ │   SSL终止       │ │   静态文件       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  应用服务层 (Rust)                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   认证授权       │ │   租户管理       │ │   聊天服务       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   WA账号管理     │ │   代理池管理     │ │   群发服务       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  数据存储层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   PostgreSQL    │ │     Redis       │ │   文件存储       │ │
│  │   (主数据库)     │ │   (缓存/会话)    │ │   (媒体文件)     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  容器运行层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  WA Container1  │ │  WA Container2  │ │  WA Container3  │ │
│  │  (租户A-账号1)   │ │  (租户A-账号2)   │ │  (租户B-账号1)   │ │
│  │  Port: 3001     │ │  Port: 3002     │ │  Port: 3003     │ │
│  │  Proxy: US-01   │ │  Proxy: EU-01   │ │  Proxy: AS-01   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 👥 用户角色

### 系统管理员 (System Admin)
- 管理所有租户和用户
- 管理代理池
- 查看系统整体运营数据
- 可以切换到任意租户进行操作

### 租户管理员 (Tenant Admin)
- 管理租户内的WhatsApp账号
- 管理客服人员
- 设置群发任务
- 查看租户内聊天数据和统计
- 可以切换到客服界面进行聊天

### 客服人员 (Customer Service)
- 处理客户聊天
- 查看分配给自己的聊天会话
- 查看客户聊天历史
- 使用快捷回复模板

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-org/whatsapp-saas-system.git
cd whatsapp-saas-system
```

### 2. 配置环境

```bash
# 复制配置文件
cp configs/config.example.yml configs/config.yml

# 编辑配置文件
vim configs/config.yml
```

### 3. 启动服务

```bash
# 开发环境
docker-compose -f docker-compose.yml up -d

# 生产环境
docker-compose -f docker-compose.prod.yml up -d
```

### 4. 访问管理界面

打开浏览器访问：http://localhost

默认系统管理员登录信息：
- 用户名：admin
- 密码：admin123

## 📖 文档目录

### 核心文档
- [需求文档](docs/requirements-document.md) - 完整的功能需求和用户界面需求
- [技术设计文档](docs/technical-design.md) - 系统架构、数据库设计和API设计
- [实施计划](docs/implementation-plan.md) - 详细的开发计划和里程碑

### 技术文档
- [API文档](docs/api-reference.md) - 完整的RESTful API参考
- [数据库设计](docs/database-schema.md) - 数据库表结构和关系
- [部署指南](docs/deployment-guide.md) - 生产环境部署指南

### 用户文档
- [系统管理员手册](docs/admin-manual.md) - 系统管理员操作指南
- [租户管理员手册](docs/tenant-manual.md) - 租户管理员操作指南
- [客服人员手册](docs/agent-manual.md) - 客服工作台使用指南

## 🔧 配置说明

### 主配置文件

```yaml
# configs/config.yml
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"

database:
  driver: "postgres"
  dsn: "******************************************/whatsapp_saas"
  max_open_conns: 50
  max_idle_conns: 10

proxy_pool:
  check_interval: "5m"
  rotation_interval: "1h"
  allocation_strategy: "least_used"
  max_retries: 3

container:
  image: "aldinokemal2104/go-whatsapp-web-multidevice"
  port_range:
    start: 3001
    end: 4000
  resources:
    cpu_limit: "0.5"
    memory_limit: "512M"

logging:
  level: "info"
  format: "json"
  output: "stdout"

security:
  jwt_secret: "your-jwt-secret-key"
  api_keys:
    - "your-api-key-1"
    - "your-api-key-2"
```

### 代理配置

```yaml
# configs/proxies.yml
proxies:
  - id: "us-001"
    name: "US East 1"
    type: "http"
    host: "proxy1.example.com"
    port: 8080
    username: "user1"
    password: "pass1"
    country: "US"
    max_users: 1

  - id: "eu-001"
    name: "EU West 1"
    type: "socks5"
    host: "proxy2.example.com"
    port: 1080
    username: "user2"
    password: "pass2"
    country: "DE"
    max_users: 2
```

## 📊 API接口

### 系统管理员API

```bash
# 创建租户
curl -X POST http://localhost/api/v1/admin/tenants \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "测试企业",
    "plan": "pro",
    "admin_email": "<EMAIL>"
  }'

# 获取租户列表
curl http://localhost/api/v1/admin/tenants \
  -H "Authorization: Bearer YOUR_TOKEN"

# 切换到租户
curl -X POST http://localhost/api/v1/admin/impersonate/USER_ID \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 租户管理API

```bash
# 创建WhatsApp账号
curl -X POST http://localhost/api/v1/tenant/whatsapp-accounts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "客服账号1",
    "phone_number": "+**********",
    "proxy_requirements": {
      "country": "US",
      "max_latency": "5s"
    }
  }'

# 创建群发任务
curl -X POST http://localhost/api/v1/tenant/broadcast-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "新年促销",
    "message_template": "新年快乐！特价商品等你来抢购！",
    "target_list": ["+**********", "+**********"],
    "schedule_time": "2024-01-01T10:00:00Z"
  }'
```

### 客服工作台API

```bash
# 获取我的聊天列表
curl http://localhost/api/v1/agent/chats \
  -H "Authorization: Bearer YOUR_TOKEN"

# 发送消息
curl -X POST http://localhost/api/v1/agent/chats/CHAT_ID/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "您好，有什么可以帮助您的吗？",
    "message_type": "text"
  }'
```

## 🔍 监控面板

### Grafana仪表板

访问 http://localhost:3000 查看监控面板：

- **系统概览**：租户状态、账号状态、代理状态、系统资源
- **业务监控**：聊天会话数、消息量、群发统计
- **性能监控**：请求量、响应时间、错误率
- **容器监控**：CPU、内存、网络使用情况
- **代理监控**：代理延迟、成功率、使用情况

### 告警规则

- 租户账号异常下线
- 代理连接失败
- 系统资源使用率过高
- 消息发送失败率超阈值
- 聊天响应时间过长

### 日志查看

```bash
# 查看应用日志
docker logs whatsapp-saas-app

# 查看特定租户的容器日志
docker logs whatsapp-tenant-001-account-001

# 查看系统日志
tail -f /var/log/whatsapp-saas/app.log
```

## 🛠️ 开发指南

### 后端开发环境

```bash
# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 克隆项目
git clone https://github.com/your-org/whatsapp-saas-system.git
cd whatsapp-saas-system

# 安装依赖
cargo build

# 运行测试
cargo test

# 启动开发服务器
cargo run

# 数据库迁移
sqlx migrate run
```

### 前端开发环境

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 运行测试
npm run test
```

### 项目结构

```
whatsapp-saas-system/
├── src/                    # Rust后端源码
│   ├── domain/            # 领域层
│   ├── application/       # 应用层
│   ├── infrastructure/    # 基础设施层
│   └── interface/         # 接口层
├── frontend/              # Vue.js前端
│   ├── src/
│   │   ├── views/         # 页面组件
│   │   ├── components/    # 通用组件
│   │   ├── stores/        # 状态管理
│   │   └── services/      # API服务
├── migrations/            # 数据库迁移
├── configs/               # 配置文件
├── docs/                  # 文档
└── deployments/           # 部署文件
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🚀 部署指南

### 生产环境部署

```bash
# 1. 准备服务器环境
sudo apt update && sudo apt install -y docker.io docker-compose

# 2. 克隆项目
git clone https://github.com/your-org/whatsapp-saas-system.git
cd whatsapp-saas-system

# 3. 配置环境变量
cp .env.example .env
vim .env

# 4. 启动服务
docker-compose -f docker-compose.prod.yml up -d

# 5. 初始化数据库
docker-compose exec app sqlx migrate run

# 6. 创建系统管理员
docker-compose exec app ./create-admin.sh
```

### 环境变量配置

```bash
# .env
DATABASE_URL=***********************************************/whatsapp_saas
REDIS_URL=redis://redis:6379
JWT_SECRET=your-jwt-secret-key
GRAFANA_PASSWORD=your-grafana-password
DB_PASSWORD=your-database-password
```

## 📈 性能指标

### 系统性能
- **并发用户**: 1000+
- **消息延迟**: < 500ms
- **API响应时间**: < 200ms
- **系统可用性**: 99.5%

### 资源使用
- **CPU使用率**: < 70%
- **内存使用率**: < 80%
- **磁盘IO**: < 80%
- **网络带宽**: < 80%

## ⚠️ 免责声明

- 本项目仅供学习和研究使用
- 请遵守WhatsApp的服务条款和当地法律法规
- 使用代理服务器时请确保合规性
- 作者不对使用本项目造成的任何后果负责
- 请确保获得适当的商业许可后再用于商业用途

## 🆘 支持

如果您遇到问题或需要帮助：

1. 查看 [技术文档](docs/technical-design.md)
2. 搜索 [Issues](https://github.com/your-org/whatsapp-saas-system/issues)
3. 创建新的 Issue 并提供详细信息
4. 联系维护者或技术支持

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [go-whatsapp-web-multidevice](https://github.com/aldinokemal/go-whatsapp-web-multidevice) - 基础WhatsApp API
- [whatsmeow](https://github.com/tulir/whatsmeow) - WhatsApp Web协议库
- [Rust](https://www.rust-lang.org/) - 系统编程语言
- [Vue.js](https://vuejs.org/) - 前端框架
- [PostgreSQL](https://www.postgresql.org/) - 数据库系统
- [Docker](https://www.docker.com/) - 容器化平台
- [Prometheus](https://prometheus.io/) - 监控系统
- [Grafana](https://grafana.com/) - 可视化面板

---

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

**📧 商业合作请联系：<EMAIL>**
