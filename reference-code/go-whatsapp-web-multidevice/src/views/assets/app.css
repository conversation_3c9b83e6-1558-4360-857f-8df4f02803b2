:root {
    --primary-color: #00ADD8; /* <PERSON><PERSON>'s brand blue */
    --secondary-color: #007d9c; /* <PERSON><PERSON>'s darker blue */
    --tertiary-color: #004d5f; /* <PERSON><PERSON>'s darkest blue */
    --background-color: #f8fbff; /* Light blue background */
    --card-hover-color: #ffffff;
    --text-color: #111B21; /* Keep text readable */
    --gradient-start: #00ADD8;
    --gradient-end: #007d9c;
    --message-background: #FFFFFF;
    --success-blue: #00ADD8;
}

body {
    background-color: var(--background-color) !important;
    color: var(--text-color) !important;
    font-family: "Helvetica Neue", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
}

.container {
    padding: 2em 1em !important;
    max-width: 1400px !important;
    margin: 0 auto !important;
}

.main-header {
    background: var(--primary-color) !important;
    color: white !important;
    padding: 1.5em 2em !important;
    border-radius: 24px !important;
    margin-bottom: 2em !important;
    box-shadow:
        0 20px 40px rgba(0, 173, 216, 0.2),
        inset 0 0 80px rgba(255, 255, 255, 0.15) !important;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease-in-out;
}

.main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(
            circle at top right,
            rgba(255,255,255,0.2) 0%,
            rgba(255,255,255,0) 60%
        ),
        linear-gradient(
            45deg,
            rgba(255,255,255,0.1) 0%,
            rgba(255,255,255,0) 70%
        );
    opacity: 0.8;
    z-index: 1;
}

.main-header::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    right: -50%;
    bottom: -50%;
    background: 
        radial-gradient(
            circle,
            rgba(255,255,255,0.1) 0%,
            transparent 70%
        );
    animation: rotate 20s linear infinite;
    z-index: 0;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.main-header .ui.header {
    position: relative;
    z-index: 2;
    margin: 0 !important;
    font-weight: 700 !important;
    font-size: clamp(1.5em, 3vw, 1.8em) !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2) !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    letter-spacing: 0.5px;
}

.main-header .title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    color: white;
}

.main-header .title-container img {
    width: 2.2em;
    height: 2.2em;
    background: white;
    padding: 0.2em;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.main-header .title-container span {
    flex: 1;
    text-align: center;
}

.main-header .title-container img {
    animation: float 4s ease-in-out infinite;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.main-header .title-container img:hover {
    transform: scale(1.1);
}

.main-header .version-label {
    margin-top: 4px !important;
    background: rgba(255,255,255,0.12);
    color: white;
    font-size: 0.5em;
    padding: 4px 10px;
    border-radius: 12px;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.25s ease;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.main-header .version-label:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

@media (min-width: 768px) {
    .main-header {
        padding: 2em !important;
    }

    .main-header .ui.header {
        flex-direction: column;
    }

    .main-header .version-label {
        margin-top: 4px !important;
    }
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-8px) rotate(5deg); }
    100% { transform: translateY(0px) rotate(0deg); }
}

.ui.header {
    font-weight: 600 !important;
    color: var(--text-color) !important;
}

.ui.cards > .card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    border-radius: 16px !important;
    margin: 0.7em !important;
    background-color: var(--message-background) !important;
    border: 1px solid rgba(0, 173, 216, 0.1);
}

.ui.cards > .card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 12px 24px rgba(0, 173, 216, 0.15) !important;
    background-color: var(--card-hover-color) !important;
}

.ui.horizontal.divider {
    font-size: 1.3em !important;
    color: var(--secondary-color) !important;
    margin: 2.5em 0 !important;
    font-weight: 700 !important;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.ui.success.message {
    border-radius: 16px !important;
    box-shadow: 0 4px 8px rgba(0, 173, 216, 0.1) !important;
    border: 1px solid rgba(0, 173, 216, 0.2) !important;
    background-color: rgba(230, 248, 255, 0.8) !important;
    color: var(--success-blue) !important;
    animation: slideIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(10px);
}

@keyframes slideIn {
    from { transform: translateX(-30px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.ui.button {
    border-radius: 12px !important;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    /* background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important; */
    /* background: var(--primary-color) !important; */
    color: white !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px;
}

.ui.button:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 16px rgba(0, 173, 216, 0.2) !important;
    filter: brightness(0.95);
}

.ui.form input, .ui.form textarea {
    border-radius: 12px !important;
    border: 2px solid rgba(0, 173, 216, 0.1) !important;
    transition: all 0.3s ease;
}

.ui.form input:focus, .ui.form textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(0, 173, 216, 0.1) !important;
}

.ui.toast-container {
    padding: 1.5em !important;
}

.ui.toast {
    border-radius: 16px !important;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
    background-color: var(--message-background) !important;
    border: 1px solid rgba(0, 173, 216, 0.1);
}

.ui.grid {
    margin: -0.7em !important;
}

.ui.grid > .column {
    padding: 0.7em !important;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.ui.cards {
    animation: fadeIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.ui.modal {
    border-radius: 20px !important;
    background-color: var(--message-background) !important;
    overflow: hidden;
}

.ui.modal > .header {
    border-radius: 20px 20px 0 0 !important;
    background: var(--gradient-start) !important;
    color: white !important;
    padding: 1.5em !important;
}

.ui.modal > .actions {
    border-radius: 0 0 20px 20px !important;
    background-color: rgba(248, 248, 248, 0.8) !important;
    backdrop-filter: blur(10px);
}

.ui.modal > .actions > .ui.button {
    background: var(--primary-color) !important;
    color: white !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px;
}

/* Splash Screen Styles */
#splash-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #00ADD8 0%, #007d9c 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-in-out;
    color: white;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

#splash-screen.fade-out {
    opacity: 0;
    pointer-events: none;
}

.splash-icon {
    width: 5em;
    height: 5em;
    margin: 0;
    animation: bounce 2s infinite, glassShimmer 4s ease-in-out infinite;
    border-radius: 20px;
    display: block;
    object-fit: cover;
    object-position: center;
    position: relative;
    z-index: 2;
    background: white;
    padding: 0.5em;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.splash-title {
    font-size: 2em;
    font-weight: bold;
    margin: 20px 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.splash-spinner {
    margin-top: 20px;
    align-self: center;
}

#splash-screen > * {
    flex-shrink: 0;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-20px); }
}

@keyframes glassShimmer {
    0%, 100% {
        filter: drop-shadow(0 8px 32px rgba(0, 0, 0, 0.15)) brightness(1);
    }
    50% {
        filter: drop-shadow(0 12px 40px rgba(0, 0, 0, 0.2)) brightness(1.05);
    }
}

/* Responsive splash icon design */
@media (max-width: 768px) {
    .splash-icon {
        width: 5em;
        height: 5em;
        border-radius: 20px;
    }
}

@media (max-width: 480px) {
    .splash-icon {
        width: 4em;
        height: 4em;
        border-radius: 16px;
    }
}

/* High-DPI/Retina display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .splash-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: optimize-contrast;
        image-rendering: crisp-edges;
    }
}