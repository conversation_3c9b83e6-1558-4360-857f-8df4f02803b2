# 部署配置文件

## 1. Dock<PERSON>配置

### 1.1 主服务Dockerfile

```dockerfile
# deployments/docker/Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o manager ./cmd/manager

FROM alpine:latest
RUN apk --no-cache add ca-certificates sqlite
WORKDIR /root/

COPY --from=builder /app/manager .
COPY --from=builder /app/web ./web
COPY --from=builder /app/configs ./configs

EXPOSE 8080
CMD ["./manager"]
```

### 1.2 Docker Compose配置

```yaml
# deployments/compose/docker-compose.yml
version: '3.8'

services:
  whatsapp-manager:
    build:
      context: ../../
      dockerfile: deployments/docker/Dockerfile
    container_name: whatsapp-manager
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - CONFIG_PATH=/app/configs/config.yml
      - LOG_LEVEL=info
      - DB_PATH=/app/data/manager.db
    networks:
      - whatsapp-network
    depends_on:
      - redis
      - prometheus

  redis:
    image: redis:7-alpine
    container_name: whatsapp-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - whatsapp-network

  prometheus:
    image: prom/prometheus:latest
    container_name: whatsapp-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - whatsapp-network

  grafana:
    image: grafana/grafana:latest
    container_name: whatsapp-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - whatsapp-network

  nginx:
    image: nginx:alpine
    container_name: whatsapp-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    networks:
      - whatsapp-network
    depends_on:
      - whatsapp-manager

networks:
  whatsapp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
```

### 1.3 生产环境配置

```yaml
# deployments/compose/docker-compose.prod.yml
version: '3.8'

services:
  whatsapp-manager:
    image: your-registry/whatsapp-manager:latest
    container_name: whatsapp-manager
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /opt/whatsapp/data:/app/data
      - /opt/whatsapp/logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - CONFIG_PATH=/app/configs/config.prod.yml
      - LOG_LEVEL=warn
      - DB_PATH=/app/data/manager.db
    networks:
      - whatsapp-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:15
    container_name: whatsapp-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=whatsapp_manager
      - POSTGRES_USER=whatsapp
      - POSTGRES_PASSWORD=your_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - whatsapp-network

volumes:
  postgres_data:
```

## 2. Nginx配置

### 2.1 反向代理配置

```nginx
# deployments/nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream whatsapp_manager {
        server whatsapp-manager:8080;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=whatsapp:10m rate=5r/s;

    server {
        listen 80;
        server_name your-domain.com;
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

        # 管理界面
        location / {
            proxy_pass http://whatsapp_manager;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API接口
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://whatsapp_manager;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WhatsApp API代理
        location /whatsapp/ {
            limit_req zone=whatsapp burst=10 nodelay;
            proxy_pass http://whatsapp_manager;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 增加超时时间
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 静态文件
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 健康检查
        location /health {
            access_log off;
            proxy_pass http://whatsapp_manager;
        }
    }
}
```

## 3. 应用配置

### 3.1 主配置文件

```yaml
# configs/config.yml
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"

database:
  driver: "sqlite3"
  dsn: "data/manager.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "1h"

proxy_pool:
  check_interval: "5m"
  rotation_interval: "1h"
  max_retries: 3
  timeout_duration: "10s"
  allow_shared_proxy: false
  max_users_per_proxy: 1
  allocation_strategy: "least_used"

container:
  image: "aldinokemal2104/go-whatsapp-web-multidevice"
  network: "whatsapp-network"
  port_range:
    start: 3001
    end: 4000
  resources:
    cpu_limit: "0.5"
    memory_limit: "512M"
    cpu_request: "0.1"
    memory_request: "128M"

logging:
  level: "info"
  format: "json"
  output: "stdout"
  file_path: "logs/app.log"
  max_size: 100
  max_backups: 10
  max_age: 30

security:
  api_keys:
    - "your-api-key-1"
    - "your-api-key-2"
  cors:
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]

monitoring:
  metrics_enabled: true
  metrics_path: "/metrics"
  health_check_path: "/health"

redis:
  addr: "redis:6379"
  password: ""
  db: 0
  pool_size: 10
```

### 3.2 生产环境配置

```yaml
# configs/config.prod.yml
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"

database:
  driver: "postgres"
  dsn: "******************************************************/whatsapp_manager?sslmode=disable"
  max_open_conns: 50
  max_idle_conns: 10
  conn_max_lifetime: "1h"

proxy_pool:
  check_interval: "3m"
  rotation_interval: "30m"
  max_retries: 5
  timeout_duration: "15s"
  allow_shared_proxy: false
  max_users_per_proxy: 1
  allocation_strategy: "latency_based"

logging:
  level: "warn"
  format: "json"
  output: "file"
  file_path: "logs/app.log"
  max_size: 500
  max_backups: 30
  max_age: 90

security:
  api_keys:
    - "${API_KEY_1}"
    - "${API_KEY_2}"
  cors:
    allowed_origins: ["https://your-domain.com"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: ["Content-Type", "Authorization", "X-API-Key"]
```

## 4. 监控配置

### 4.1 Prometheus配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'whatsapp-manager'
    static_configs:
      - targets: ['whatsapp-manager:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'docker'
    static_configs:
      - targets: ['docker-exporter:9323']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 4.2 告警规则

```yaml
# monitoring/rules/alerts.yml
groups:
  - name: whatsapp-manager
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      - alert: AccountDown
        expr: whatsapp_account_status{status!="running"} > 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "WhatsApp account is down"
          description: "Account {{ $labels.account_id }} is not running"

      - alert: ProxyFailure
        expr: whatsapp_proxy_status{status="failed"} > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Proxy failure detected"
          description: "Proxy {{ $labels.proxy_id }} has failed"

      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Container {{ $labels.name }} memory usage is {{ $value }}%"
```

### 4.3 Grafana仪表板

```json
{
  "dashboard": {
    "id": null,
    "title": "WhatsApp Manager Dashboard",
    "tags": ["whatsapp", "manager"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Account Status",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(whatsapp_accounts_total)",
            "legendFormat": "Total Accounts"
          },
          {
            "expr": "sum(whatsapp_accounts_total{status=\"running\"})",
            "legendFormat": "Running"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Proxy Status",
        "type": "piechart",
        "targets": [
          {
            "expr": "sum by (status) (whatsapp_proxies_total)",
            "legendFormat": "{{ status }}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{ method }} {{ status }}"
          }
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

## 5. 数据库初始化

### 5.1 SQLite初始化

```sql
-- sql/sqlite/init.sql
CREATE TABLE IF NOT EXISTS accounts (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    container_id TEXT,
    port INTEGER,
    proxy_id TEXT,
    status TEXT NOT NULL DEFAULT 'creating',
    config TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_seen DATETIME
);

CREATE TABLE IF NOT EXISTS proxies (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    host TEXT NOT NULL,
    port INTEGER NOT NULL,
    username TEXT,
    password TEXT,
    country TEXT,
    region TEXT,
    status TEXT NOT NULL DEFAULT 'inactive',
    last_check DATETIME,
    latency INTEGER DEFAULT 0,
    success_rate REAL DEFAULT 0.0,
    in_use BOOLEAN DEFAULT FALSE,
    assigned_to TEXT,
    max_users INTEGER DEFAULT 1,
    current_users INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS proxy_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proxy_id TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    latency INTEGER,
    success BOOLEAN,
    error_message TEXT,
    FOREIGN KEY (proxy_id) REFERENCES proxies(id)
);

CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status);
CREATE INDEX IF NOT EXISTS idx_proxies_status ON proxies(status);
CREATE INDEX IF NOT EXISTS idx_proxy_stats_timestamp ON proxy_stats(timestamp);
```

### 5.2 PostgreSQL初始化

```sql
-- sql/postgres/init.sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS accounts (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    container_id VARCHAR(255),
    port INTEGER,
    proxy_id VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'creating',
    config JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS proxies (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    username VARCHAR(255),
    password VARCHAR(255),
    country VARCHAR(10),
    region VARCHAR(50),
    status VARCHAR(50) NOT NULL DEFAULT 'inactive',
    last_check TIMESTAMP WITH TIME ZONE,
    latency BIGINT DEFAULT 0,
    success_rate DECIMAL(5,4) DEFAULT 0.0,
    in_use BOOLEAN DEFAULT FALSE,
    assigned_to VARCHAR(255),
    max_users INTEGER DEFAULT 1,
    current_users INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS proxy_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    proxy_id VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    latency BIGINT,
    success BOOLEAN,
    error_message TEXT,
    FOREIGN KEY (proxy_id) REFERENCES proxies(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status);
CREATE INDEX IF NOT EXISTS idx_proxies_status ON proxies(status);
CREATE INDEX IF NOT EXISTS idx_proxy_stats_timestamp ON proxy_stats(timestamp);

-- 自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_proxies_updated_at BEFORE UPDATE ON proxies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 6. 部署脚本

### 6.1 部署脚本

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

# 配置
ENVIRONMENT=${1:-development}
COMPOSE_FILE="deployments/compose/docker-compose.yml"

if [ "$ENVIRONMENT" = "production" ]; then
    COMPOSE_FILE="deployments/compose/docker-compose.prod.yml"
fi

echo "Deploying WhatsApp Manager in $ENVIRONMENT mode..."

# 创建必要的目录
mkdir -p data logs

# 设置权限
chmod 755 data logs

# 构建和启动服务
docker-compose -f $COMPOSE_FILE up -d --build

# 等待服务启动
echo "Waiting for services to start..."
sleep 30

# 健康检查
echo "Performing health check..."
if curl -f http://localhost:8080/health; then
    echo "✅ Deployment successful!"
else
    echo "❌ Health check failed!"
    exit 1
fi

echo "🚀 WhatsApp Manager is running at http://localhost:8080"
```

### 6.2 备份脚本

```bash
#!/bin/bash
# scripts/backup.sh

set -e

BACKUP_DIR="/opt/backups/whatsapp-manager"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/backup_$DATE.tar.gz"

echo "Creating backup..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 停止服务（可选）
# docker-compose stop whatsapp-manager

# 创建备份
tar -czf $BACKUP_FILE \
    data/ \
    configs/ \
    logs/ \
    --exclude='logs/*.log'

# 重启服务（如果之前停止了）
# docker-compose start whatsapp-manager

echo "Backup created: $BACKUP_FILE"

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +30 -delete

echo "✅ Backup completed!"
```

这些配置文件提供了：

1. **完整的Docker部署**：开发和生产环境配置
2. **Nginx反向代理**：负载均衡、SSL、安全头
3. **监控系统**：Prometheus、Grafana、告警规则
4. **数据库初始化**：SQLite和PostgreSQL支持
5. **自动化脚本**：部署、备份、维护脚本
6. **安全配置**：API密钥、CORS、限流
7. **日志管理**：结构化日志、轮转配置

通过这些配置，可以快速部署一个生产就绪的多容器WhatsApp管理系统。
