# WhatsApp多租户SaaS系统 - 技术设计文档

## 1. 系统架构设计

### 1.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Vue.js)                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   系统管理台     │ │   租户管理台     │ │   客服工作台     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │ HTTP/WebSocket
┌─────────────────────────────────────────────────────────────┐
│                  API网关层 (Nginx)                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   负载均衡       │ │   SSL终止       │ │   静态文件       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  应用服务层 (Rust)                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   认证授权       │ │   租户管理       │ │   聊天服务       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   WA账号管理     │ │   代理池管理     │ │   群发服务       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  数据存储层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   PostgreSQL    │ │     Redis       │ │   文件存储       │ │
│  │   (主数据库)     │ │   (缓存/会话)    │ │   (媒体文件)     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  容器运行层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  WA Container1  │ │  WA Container2  │ │  WA Container3  │ │
│  │  (租户A-账号1)   │ │  (租户A-账号2)   │ │  (租户B-账号1)   │ │
│  │  Port: 3001     │ │  Port: 3002     │ │  Port: 3003     │ │
│  │  Proxy: US-01   │ │  Proxy: EU-01   │ │  Proxy: AS-01   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择

#### 1.2.1 后端技术栈
- **语言**: Rust 1.75+
- **Web框架**: Axum 0.7
- **异步运行时**: Tokio 1.0
- **数据库ORM**: SQLx 0.7
- **序列化**: Serde 1.0
- **HTTP客户端**: Reqwest 0.11
- **WebSocket**: Tokio-tungstenite
- **容器管理**: Bollard (Docker API)

#### 1.2.2 前端技术栈
- **框架**: Vue.js 3.4
- **语言**: TypeScript 5.0
- **构建工具**: Vite 5.0
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **WebSocket**: Native WebSocket API

#### 1.2.3 基础设施
- **数据库**: PostgreSQL 15
- **缓存**: Redis 7
- **反向代理**: Nginx 1.24
- **容器**: Docker 24.0
- **编排**: Docker Compose
- **监控**: Prometheus + Grafana

### 1.3 模块化设计

#### 1.3.1 领域驱动设计 (DDD)
```
src/
├── domain/                 # 领域层 - 业务逻辑
│   ├── tenant/            # 租户领域
│   ├── user/              # 用户领域
│   ├── whatsapp/          # WhatsApp领域
│   ├── chat/              # 聊天领域
│   ├── broadcast/         # 群发领域
│   └── proxy/             # 代理领域
├── application/           # 应用层 - 用例编排
│   ├── tenant_service.rs
│   ├── whatsapp_service.rs
│   ├── chat_service.rs
│   └── broadcast_service.rs
├── infrastructure/        # 基础设施层
│   ├── database/          # 数据库实现
│   ├── container/         # 容器管理
│   ├── proxy/             # 代理管理
│   └── websocket/         # WebSocket实现
└── interface/             # 接口层
    ├── http/              # HTTP API
    ├── websocket/         # WebSocket API
    └── grpc/              # gRPC API (可选)
```

## 2. 核心模块设计

### 2.1 认证授权模块

#### 2.1.1 JWT Token设计
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,           // 用户ID
    pub tenant_id: Option<String>, // 租户ID
    pub role: UserRole,        // 用户角色
    pub permissions: Vec<String>, // 权限列表
    pub exp: usize,            // 过期时间
    pub iat: usize,            // 签发时间
    pub impersonated_by: Option<String>, // 代理用户ID
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UserRole {
    SystemAdmin,
    TenantAdmin,
    CustomerService,
}
```

#### 2.1.2 权限控制
```rust
// 权限中间件
pub async fn auth_middleware(
    mut req: Request<Body>,
    next: Next<Body>,
) -> Result<Response<Body>, StatusCode> {
    let token = extract_bearer_token(&req)?;
    let claims = verify_jwt_token(&token).await?;
    
    // 注入用户信息到请求上下文
    req.extensions_mut().insert(AuthContext {
        user_id: claims.sub,
        tenant_id: claims.tenant_id,
        role: claims.role,
        permissions: claims.permissions,
        impersonated_by: claims.impersonated_by,
    });
    
    Ok(next.run(req).await)
}

// 角色检查
pub fn require_role(required_role: UserRole) -> impl Filter<Extract = (), Error = Rejection> {
    warp::any()
        .and(warp::ext::get::<AuthContext>())
        .and_then(move |auth: AuthContext| async move {
            if auth.role >= required_role {
                Ok(())
            } else {
                Err(warp::reject::custom(InsufficientPermissions))
            }
        })
}
```

### 2.2 多租户数据隔离

#### 2.2.1 数据库级别隔离
```rust
// 租户上下文
#[derive(Debug, Clone)]
pub struct TenantContext {
    pub tenant_id: TenantId,
    pub user_id: UserId,
    pub role: UserRole,
}

// 自动注入租户过滤条件
pub trait TenantAwareRepository {
    async fn find_by_tenant<T>(&self, tenant_id: TenantId, query: Query) -> Result<Vec<T>, Error>;
}

impl TenantAwareRepository for SqlRepository {
    async fn find_by_tenant<T>(&self, tenant_id: TenantId, mut query: Query) -> Result<Vec<T>, Error> {
        // 自动添加租户过滤条件
        query.add_filter("tenant_id", tenant_id);
        self.execute_query(query).await
    }
}
```

#### 2.2.2 端口资源隔离
```rust
pub struct PortManager {
    allocations: Arc<RwLock<HashMap<TenantId, PortRange>>>,
    pool: Arc<RwLock<PortPool>>,
}

impl PortManager {
    pub async fn allocate_for_tenant(&self, tenant_id: TenantId) -> Result<u16, Error> {
        let mut allocations = self.allocations.write().await;
        let tenant_range = allocations.get_mut(&tenant_id)
            .ok_or(Error::TenantNotFound)?;
        
        if tenant_range.used >= tenant_range.quota {
            return Err(Error::PortQuotaExceeded);
        }
        
        let port = self.pool.write().await.allocate_in_range(tenant_range.start, tenant_range.end)?;
        tenant_range.used += 1;
        
        Ok(port)
    }
}
```

### 2.3 WhatsApp容器管理

#### 2.3.1 容器生命周期管理
```rust
pub struct ContainerManager {
    docker: Docker,
    proxy_pool: Arc<ProxyPool>,
    port_manager: Arc<PortManager>,
}

impl ContainerManager {
    pub async fn create_whatsapp_container(
        &self,
        account: &WhatsAppAccount,
        tenant_id: TenantId,
    ) -> Result<String, Error> {
        // 1. 分配代理
        let proxy = self.proxy_pool.allocate_for_tenant(tenant_id).await?;
        
        // 2. 构建容器配置
        let config = ContainerConfig {
            image: "aldinokemal2104/go-whatsapp-web-multidevice".to_string(),
            env: self.build_environment_variables(account, &proxy),
            ports: self.build_port_mappings(account.port),
            volumes: self.build_volume_mappings(account.id.clone()),
            labels: self.build_labels(account, tenant_id),
            restart_policy: RestartPolicy::UnlessStopped,
            resources: ResourceLimits {
                memory: 512 * 1024 * 1024, // 512MB
                cpu_quota: 50000,          // 0.5 CPU
            },
        };
        
        // 3. 创建并启动容器
        let container = self.docker.create_container(
            Some(CreateContainerOptions {
                name: format!("whatsapp-{}-{}", tenant_id, account.id),
            }),
            config,
        ).await?;
        
        self.docker.start_container(&container.id, None::<StartContainerOptions<String>>).await?;
        
        Ok(container.id)
    }
    
    fn build_environment_variables(&self, account: &WhatsAppAccount, proxy: &Proxy) -> Vec<String> {
        vec![
            "APP_PORT=3000".to_string(),
            "APP_DEBUG=false".to_string(),
            format!("HTTP_PROXY={}", proxy.url()),
            format!("HTTPS_PROXY={}", proxy.url()),
            format!("WHATSAPP_WEBHOOK={}", account.webhook_url()),
            format!("APP_BASIC_AUTH={}", account.basic_auth()),
        ]
    }
}
```

#### 2.3.2 容器健康检查
```rust
pub struct HealthChecker {
    docker: Docker,
    http_client: reqwest::Client,
}

impl HealthChecker {
    pub async fn check_container_health(&self, container_id: &str) -> HealthStatus {
        // 1. 检查容器状态
        let container_info = match self.docker.inspect_container(container_id, None).await {
            Ok(info) => info,
            Err(_) => return HealthStatus::NotFound,
        };
        
        if !container_info.state.unwrap().running.unwrap_or(false) {
            return HealthStatus::Stopped;
        }
        
        // 2. 检查应用健康状态
        let port = self.extract_port_from_container(&container_info);
        let health_url = format!("http://localhost:{}/app/devices", port);
        
        match self.http_client.get(&health_url).send().await {
            Ok(response) if response.status().is_success() => HealthStatus::Healthy,
            Ok(_) => HealthStatus::Unhealthy,
            Err(_) => HealthStatus::Unreachable,
        }
    }
    
    pub async fn start_health_monitoring(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(30));
        
        loop {
            interval.tick().await;
            self.check_all_containers().await;
        }
    }
}
```

### 2.4 实时聊天系统

#### 2.4.1 WebSocket连接管理
```rust
pub struct WebSocketManager {
    connections: Arc<RwLock<HashMap<UserId, WebSocketConnection>>>,
    message_broker: Arc<MessageBroker>,
}

#[derive(Debug)]
pub struct WebSocketConnection {
    pub user_id: UserId,
    pub tenant_id: TenantId,
    pub role: UserRole,
    pub sender: mpsc::UnboundedSender<Message>,
    pub connected_at: DateTime<Utc>,
}

impl WebSocketManager {
    pub async fn handle_connection(
        &self,
        ws: WebSocket,
        auth_context: AuthContext,
    ) -> Result<(), Error> {
        let (ws_sender, mut ws_receiver) = ws.split();
        let (tx, rx) = mpsc::unbounded_channel();
        
        // 注册连接
        let connection = WebSocketConnection {
            user_id: auth_context.user_id.clone(),
            tenant_id: auth_context.tenant_id.clone(),
            role: auth_context.role,
            sender: tx,
            connected_at: Utc::now(),
        };
        
        self.connections.write().await.insert(auth_context.user_id.clone(), connection);
        
        // 启动消息转发任务
        let forward_task = self.start_message_forwarding(rx, ws_sender);
        let receive_task = self.start_message_receiving(ws_receiver, auth_context.clone());
        
        // 等待任务完成
        tokio::select! {
            _ = forward_task => {},
            _ = receive_task => {},
        }
        
        // 清理连接
        self.connections.write().await.remove(&auth_context.user_id);
        
        Ok(())
    }
    
    pub async fn broadcast_to_tenant(&self, tenant_id: TenantId, message: Message) {
        let connections = self.connections.read().await;
        
        for connection in connections.values() {
            if connection.tenant_id == tenant_id {
                let _ = connection.sender.send(message.clone());
            }
        }
    }
    
    pub async fn send_to_user(&self, user_id: UserId, message: Message) {
        let connections = self.connections.read().await;
        
        if let Some(connection) = connections.get(&user_id) {
            let _ = connection.sender.send(message);
        }
    }
}
```

#### 2.4.2 消息处理流程
```rust
pub struct MessageProcessor {
    whatsapp_service: Arc<WhatsAppService>,
    chat_service: Arc<ChatService>,
    websocket_manager: Arc<WebSocketManager>,
}

impl MessageProcessor {
    pub async fn process_incoming_message(&self, webhook_data: WebhookData) -> Result<(), Error> {
        // 1. 解析WhatsApp消息
        let wa_message = self.parse_whatsapp_message(webhook_data)?;
        
        // 2. 查找或创建聊天会话
        let session = self.chat_service
            .find_or_create_session(&wa_message.from, &wa_message.account_id)
            .await?;
        
        // 3. 保存消息到数据库
        let message = Message {
            id: MessageId::new(),
            session_id: session.id,
            content: wa_message.content,
            message_type: wa_message.message_type,
            direction: MessageDirection::Inbound,
            whatsapp_message_id: Some(wa_message.id),
            created_at: Utc::now(),
        };
        
        self.chat_service.save_message(&message).await?;
        
        // 4. 实时推送给客服
        if let Some(assigned_agent) = session.assigned_agent {
            self.websocket_manager.send_to_user(
                assigned_agent,
                WebSocketMessage::NewMessage(message.clone()),
            ).await;
        } else {
            // 推送给租户管理员进行分配
            self.websocket_manager.broadcast_to_tenant(
                session.tenant_id,
                WebSocketMessage::UnassignedMessage(message.clone()),
            ).await;
        }
        
        Ok(())
    }
    
    pub async fn process_outgoing_message(&self, cmd: SendMessageCommand) -> Result<MessageId, Error> {
        // 1. 验证权限
        self.verify_send_permission(&cmd).await?;
        
        // 2. 获取WhatsApp账号
        let session = self.chat_service.get_session(cmd.session_id).await?;
        let wa_account = self.whatsapp_service.get_account(session.whatsapp_account_id).await?;
        
        // 3. 通过容器发送消息
        let wa_message_id = self.send_via_whatsapp_container(&wa_account, &cmd).await?;
        
        // 4. 保存消息记录
        let message = Message {
            id: MessageId::new(),
            session_id: cmd.session_id,
            sender_id: Some(cmd.sender_id),
            content: cmd.content,
            message_type: MessageType::Text,
            direction: MessageDirection::Outbound,
            whatsapp_message_id: Some(wa_message_id),
            created_at: Utc::now(),
        };
        
        self.chat_service.save_message(&message).await?;
        
        // 5. 实时推送消息状态
        self.websocket_manager.send_to_user(
            cmd.sender_id,
            WebSocketMessage::MessageSent(message.clone()),
        ).await;
        
        Ok(message.id)
    }
}
```

### 2.5 群发营销系统

#### 2.5.1 任务调度器
```rust
pub struct BroadcastScheduler {
    task_queue: Arc<Mutex<VecDeque<BroadcastTask>>>,
    executor: Arc<BroadcastExecutor>,
    scheduler: Arc<CronScheduler>,
}

impl BroadcastScheduler {
    pub async fn schedule_task(&self, task: BroadcastTask) -> Result<(), Error> {
        match task.schedule_time {
            Some(schedule_time) => {
                // 定时任务
                self.scheduler.schedule_at(schedule_time, task).await?;
            }
            None => {
                // 立即执行
                self.task_queue.lock().await.push_back(task);
            }
        }
        
        Ok(())
    }
    
    pub async fn start_processing(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(1));
        
        loop {
            interval.tick().await;
            
            // 处理队列中的任务
            while let Some(task) = self.task_queue.lock().await.pop_front() {
                let executor = Arc::clone(&self.executor);
                tokio::spawn(async move {
                    if let Err(e) = executor.execute_broadcast(task).await {
                        error!("Failed to execute broadcast task: {}", e);
                    }
                });
            }
        }
    }
}

pub struct BroadcastExecutor {
    whatsapp_service: Arc<WhatsAppService>,
    rate_limiter: Arc<RateLimiter>,
}

impl BroadcastExecutor {
    pub async fn execute_broadcast(&self, task: BroadcastTask) -> Result<(), Error> {
        let wa_accounts = self.whatsapp_service
            .get_accounts_by_tenant(task.tenant_id)
            .await?;
        
        // 并发发送，但限制速率
        let semaphore = Arc::new(Semaphore::new(5)); // 最多5个并发
        let tasks: Vec<_> = task.targets.into_iter()
            .map(|target| {
                let semaphore = Arc::clone(&semaphore);
                let accounts = wa_accounts.clone();
                let message = task.message_template.clone();
                let rate_limiter = Arc::clone(&self.rate_limiter);
                
                tokio::spawn(async move {
                    let _permit = semaphore.acquire().await.unwrap();
                    
                    // 速率限制
                    rate_limiter.wait().await;
                    
                    // 选择账号发送
                    let account = select_account_for_target(&accounts, &target);
                    send_message_to_target(account, &target, &message).await
                })
            })
            .collect();
        
        // 等待所有任务完成
        let results = futures::future::join_all(tasks).await;
        
        // 统计结果
        let mut success_count = 0;
        let mut failure_count = 0;
        
        for result in results {
            match result {
                Ok(Ok(_)) => success_count += 1,
                _ => failure_count += 1,
            }
        }
        
        // 更新任务状态
        self.update_task_result(task.id, success_count, failure_count).await?;
        
        Ok(())
    }
}
```

## 3. 数据库设计

### 3.1 数据库表结构

#### 3.1.1 核心业务表
```sql
-- 租户表
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    plan VARCHAR(50) NOT NULL CHECK (plan IN ('basic', 'pro', 'enterprise')),
    port_quota INTEGER NOT NULL DEFAULT 5,
    used_ports INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'deleted')),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('system_admin', 'tenant_admin', 'customer_service')),
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'deleted')),
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- WhatsApp账号表
CREATE TABLE whatsapp_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(50) NOT NULL,
    container_id VARCHAR(255),
    port INTEGER NOT NULL,
    proxy_id UUID REFERENCES proxies(id),
    status VARCHAR(50) NOT NULL DEFAULT 'creating' CHECK (status IN ('creating', 'active', 'inactive', 'failed', 'deleted')),
    assigned_to UUID REFERENCES users(id),
    webhook_url TEXT,
    basic_auth VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(tenant_id, port),
    UNIQUE(tenant_id, phone_number)
);
```

#### 3.1.2 聊天相关表
```sql
-- 聊天会话表
CREATE TABLE chat_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    whatsapp_account_id UUID NOT NULL REFERENCES whatsapp_accounts(id) ON DELETE CASCADE,
    customer_phone VARCHAR(50) NOT NULL,
    customer_name VARCHAR(255),
    assigned_agent UUID REFERENCES users(id),
    status VARCHAR(50) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'closed', 'transferred')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(whatsapp_account_id, customer_phone)
);

-- 消息表
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id),
    content TEXT NOT NULL,
    message_type VARCHAR(50) NOT NULL DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'audio', 'video', 'document', 'location', 'contact')),
    direction VARCHAR(20) NOT NULL CHECK (direction IN ('inbound', 'outbound')),
    whatsapp_message_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 消息索引
CREATE INDEX idx_messages_session_created ON messages(session_id, created_at DESC);
CREATE INDEX idx_messages_whatsapp_id ON messages(whatsapp_message_id) WHERE whatsapp_message_id IS NOT NULL;
```

#### 3.1.3 群发相关表
```sql
-- 群发任务表
CREATE TABLE broadcast_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    message_template TEXT NOT NULL,
    target_list JSONB NOT NULL,
    whatsapp_account_ids UUID[] NOT NULL,
    schedule_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    total_targets INTEGER NOT NULL DEFAULT 0,
    success_count INTEGER NOT NULL DEFAULT 0,
    failure_count INTEGER NOT NULL DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 群发记录表
CREATE TABLE broadcast_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES broadcast_tasks(id) ON DELETE CASCADE,
    whatsapp_account_id UUID NOT NULL REFERENCES whatsapp_accounts(id),
    target_phone VARCHAR(50) NOT NULL,
    message_content TEXT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed')),
    sent_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 群发索引
CREATE INDEX idx_broadcast_records_task_status ON broadcast_records(task_id, status);
CREATE INDEX idx_broadcast_tasks_tenant_status ON broadcast_tasks(tenant_id, status);
```

### 3.2 数据库优化

#### 3.2.1 索引策略
```sql
-- 租户相关索引
CREATE INDEX idx_users_tenant_role ON users(tenant_id, role) WHERE status = 'active';
CREATE INDEX idx_whatsapp_accounts_tenant_status ON whatsapp_accounts(tenant_id, status);

-- 聊天相关索引
CREATE INDEX idx_chat_sessions_agent_status ON chat_sessions(assigned_agent, status) WHERE assigned_agent IS NOT NULL;
CREATE INDEX idx_chat_sessions_account_updated ON chat_sessions(whatsapp_account_id, updated_at DESC);

-- 代理相关索引
CREATE INDEX idx_proxies_status_country ON proxies(status, country) WHERE status = 'active';
```

#### 3.2.2 分区策略
```sql
-- 消息表按时间分区
CREATE TABLE messages_y2024m01 PARTITION OF messages
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE messages_y2024m02 PARTITION OF messages
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

-- 自动创建分区的函数
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
    partition_name text;
    end_date date;
BEGIN
    partition_name := table_name || '_y' || to_char(start_date, 'YYYY') || 'm' || to_char(start_date, 'MM');
    end_date := start_date + interval '1 month';
    
    EXECUTE format('CREATE TABLE %I PARTITION OF %I FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;
```

## 4. API设计

### 4.1 RESTful API规范

#### 4.1.1 URL设计规范
```
# 系统管理员API
GET    /api/v1/admin/tenants                    # 获取租户列表
POST   /api/v1/admin/tenants                    # 创建租户
GET    /api/v1/admin/tenants/{id}               # 获取租户详情
PUT    /api/v1/admin/tenants/{id}               # 更新租户
DELETE /api/v1/admin/tenants/{id}               # 删除租户
POST   /api/v1/admin/impersonate/{user_id}     # 切换用户

# 租户管理API
GET    /api/v1/tenant/whatsapp-accounts         # 获取WA账号列表
POST   /api/v1/tenant/whatsapp-accounts         # 创建WA账号
GET    /api/v1/tenant/whatsapp-accounts/{id}    # 获取WA账号详情
PUT    /api/v1/tenant/whatsapp-accounts/{id}    # 更新WA账号
DELETE /api/v1/tenant/whatsapp-accounts/{id}    # 删除WA账号

GET    /api/v1/tenant/agents                    # 获取客服列表
POST   /api/v1/tenant/agents                    # 创建客服
PUT    /api/v1/tenant/agents/{id}               # 更新客服

GET    /api/v1/tenant/broadcast-tasks           # 获取群发任务
POST   /api/v1/tenant/broadcast-tasks           # 创建群发任务
POST   /api/v1/tenant/broadcast-tasks/{id}/execute # 执行群发任务

# 客服工作台API
GET    /api/v1/agent/chats                      # 获取我的聊天列表
GET    /api/v1/agent/chats/{id}/messages        # 获取聊天消息
POST   /api/v1/agent/chats/{id}/messages        # 发送消息
POST   /api/v1/agent/chats/{id}/assign          # 接管聊天

# WhatsApp API代理
POST   /api/v1/whatsapp/{account_id}/send/message    # 发送消息
GET    /api/v1/whatsapp/{account_id}/user/info       # 获取用户信息
POST   /api/v1/whatsapp/{account_id}/webhook         # Webhook接收
```

#### 4.1.2 响应格式规范
```rust
#[derive(Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<ApiError>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Serialize)]
pub struct ApiError {
    pub code: String,
    pub message: String,
    pub details: Option<serde_json::Value>,
}

#[derive(Serialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}
```

### 4.2 WebSocket API设计

#### 4.2.1 消息格式
```rust
#[derive(Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WebSocketMessage {
    // 认证
    Auth { token: String },
    AuthSuccess { user_id: String },
    AuthError { message: String },
    
    // 聊天消息
    NewMessage { session_id: String, message: Message },
    MessageSent { message_id: String, status: String },
    MessageDelivered { message_id: String },
    MessageRead { message_id: String },
    
    // 会话管理
    SessionAssigned { session_id: String, agent_id: String },
    SessionTransferred { session_id: String, from_agent: String, to_agent: String },
    SessionClosed { session_id: String },
    
    // 状态更新
    AgentOnline { agent_id: String },
    AgentOffline { agent_id: String },
    TypingStart { session_id: String, user_id: String },
    TypingStop { session_id: String, user_id: String },
    
    // 系统通知
    SystemNotification { title: String, message: String, level: String },
    
    // 心跳
    Ping,
    Pong,
}
```

#### 4.2.2 连接管理
```rust
pub struct WebSocketHandler {
    connections: Arc<RwLock<HashMap<UserId, Connection>>>,
    message_broker: Arc<MessageBroker>,
}

impl WebSocketHandler {
    pub async fn handle_message(&self, user_id: UserId, message: WebSocketMessage) -> Result<(), Error> {
        match message {
            WebSocketMessage::Auth { token } => {
                self.authenticate_connection(user_id, token).await
            }
            WebSocketMessage::NewMessage { session_id, message } => {
                self.handle_new_message(user_id, session_id, message).await
            }
            WebSocketMessage::TypingStart { session_id, .. } => {
                self.broadcast_typing_status(session_id, user_id, true).await
            }
            WebSocketMessage::Ping => {
                self.send_to_user(user_id, WebSocketMessage::Pong).await
            }
            _ => Ok(()),
        }
    }
}
```

## 5. 前端架构设计

### 5.1 Vue.js应用架构

#### 5.1.1 项目结构
```
frontend/
├── src/
│   ├── main.ts                 # 应用入口
│   ├── App.vue                 # 根组件
│   ├── router/                 # 路由配置
│   │   ├── index.ts
│   │   ├── admin.ts            # 系统管理员路由
│   │   ├── tenant.ts           # 租户管理路由
│   │   └── agent.ts            # 客服路由
│   ├── stores/                 # Pinia状态管理
│   │   ├── auth.ts             # 认证状态
│   │   ├── tenant.ts           # 租户状态
│   │   ├── chat.ts             # 聊天状态
│   │   └── websocket.ts        # WebSocket状态
│   ├── views/                  # 页面组件
│   │   ├── admin/              # 系统管理员页面
│   │   ├── tenant/             # 租户管理页面
│   │   └── agent/              # 客服工作台页面
│   ├── components/             # 通用组件
│   │   ├── common/             # 基础组件
│   │   ├── chat/               # 聊天相关组件
│   │   └── forms/              # 表单组件
│   ├── composables/            # 组合式函数
│   │   ├── useAuth.ts          # 认证逻辑
│   │   ├── useWebSocket.ts     # WebSocket逻辑
│   │   └── useChat.ts          # 聊天逻辑
│   ├── services/               # API服务
│   │   ├── api.ts              # API客户端
│   │   ├── auth.ts             # 认证服务
│   │   ├── tenant.ts           # 租户服务
│   │   └── chat.ts             # 聊天服务
│   └── utils/                  # 工具函数
│       ├── request.ts          # HTTP请求封装
│       ├── websocket.ts        # WebSocket封装
│       └── helpers.ts          # 辅助函数
├── public/                     # 静态资源
└── dist/                       # 构建输出
```

#### 5.1.2 状态管理设计
```typescript
// stores/auth.ts
import { defineStore } from 'pinia'

export interface User {
  id: string
  username: string
  email: string
  role: 'system_admin' | 'tenant_admin' | 'customer_service'
  tenantId?: string
  permissions: string[]
  impersonatedBy?: string
}

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null as User | null,
    token: localStorage.getItem('auth_token'),
    isAuthenticated: false,
    loading: false,
  }),

  getters: {
    isSystemAdmin: (state) => state.user?.role === 'system_admin',
    isTenantAdmin: (state) => state.user?.role === 'tenant_admin',
    isAgent: (state) => state.user?.role === 'customer_service',
    canImpersonate: (state) => state.user?.role !== 'customer_service',
    isImpersonating: (state) => !!state.user?.impersonatedBy,
  },

  actions: {
    async login(credentials: LoginCredentials) {
      this.loading = true
      try {
        const response = await authService.login(credentials)
        this.token = response.token
        this.user = response.user
        this.isAuthenticated = true
        localStorage.setItem('auth_token', response.token)
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    async impersonate(userId: string) {
      const response = await authService.impersonate(userId)
      this.token = response.token
      this.user = response.user
      localStorage.setItem('auth_token', response.token)
    },

    async stopImpersonation() {
      if (this.user?.impersonatedBy) {
        const response = await authService.stopImpersonation()
        this.token = response.token
        this.user = response.user
        localStorage.setItem('auth_token', response.token)
      }
    },

    logout() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      localStorage.removeItem('auth_token')
    },
  },
})

// stores/chat.ts
export const useChatStore = defineStore('chat', {
  state: () => ({
    sessions: [] as ChatSession[],
    activeSessionId: null as string | null,
    messages: new Map<string, Message[]>(),
    unreadCounts: new Map<string, number>(),
    typingUsers: new Map<string, Set<string>>(),
  }),

  getters: {
    activeSession: (state) =>
      state.sessions.find(s => s.id === state.activeSessionId),

    activeMessages: (state) =>
      state.activeSessionId ? state.messages.get(state.activeSessionId) || [] : [],

    totalUnreadCount: (state) =>
      Array.from(state.unreadCounts.values()).reduce((sum, count) => sum + count, 0),
  },

  actions: {
    setActiveSession(sessionId: string) {
      this.activeSessionId = sessionId
      this.markAsRead(sessionId)
    },

    addMessage(message: Message) {
      const sessionMessages = this.messages.get(message.sessionId) || []
      sessionMessages.push(message)
      this.messages.set(message.sessionId, sessionMessages)

      // 更新未读计数
      if (message.direction === 'inbound' && message.sessionId !== this.activeSessionId) {
        const currentCount = this.unreadCounts.get(message.sessionId) || 0
        this.unreadCounts.set(message.sessionId, currentCount + 1)
      }
    },

    markAsRead(sessionId: string) {
      this.unreadCounts.set(sessionId, 0)
    },

    setTyping(sessionId: string, userId: string, isTyping: boolean) {
      const typingSet = this.typingUsers.get(sessionId) || new Set()
      if (isTyping) {
        typingSet.add(userId)
      } else {
        typingSet.delete(userId)
      }
      this.typingUsers.set(sessionId, typingSet)
    },
  },
})
```

#### 5.1.3 WebSocket集成
```typescript
// composables/useWebSocket.ts
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'

export function useWebSocket() {
  const ws = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5

  const authStore = useAuthStore()
  const chatStore = useChatStore()

  const connect = () => {
    if (!authStore.token) return

    const wsUrl = `${import.meta.env.VITE_WS_URL}/ws`
    ws.value = new WebSocket(wsUrl)

    ws.value.onopen = () => {
      isConnected.value = true
      reconnectAttempts.value = 0

      // 发送认证消息
      send({
        type: 'Auth',
        token: authStore.token,
      })
    }

    ws.value.onmessage = (event) => {
      const message = JSON.parse(event.data)
      handleMessage(message)
    }

    ws.value.onclose = () => {
      isConnected.value = false

      // 自动重连
      if (reconnectAttempts.value < maxReconnectAttempts) {
        setTimeout(() => {
          reconnectAttempts.value++
          connect()
        }, Math.pow(2, reconnectAttempts.value) * 1000)
      }
    }

    ws.value.onerror = (error) => {
      console.error('WebSocket error:', error)
    }
  }

  const send = (message: any) => {
    if (ws.value?.readyState === WebSocket.OPEN) {
      ws.value.send(JSON.stringify(message))
    }
  }

  const handleMessage = (message: any) => {
    switch (message.type) {
      case 'NewMessage':
        chatStore.addMessage(message.message)
        break

      case 'SessionAssigned':
        // 处理会话分配
        break

      case 'TypingStart':
        chatStore.setTyping(message.sessionId, message.userId, true)
        break

      case 'TypingStop':
        chatStore.setTyping(message.sessionId, message.userId, false)
        break

      case 'SystemNotification':
        // 显示系统通知
        break
    }
  }

  const sendMessage = (sessionId: string, content: string) => {
    send({
      type: 'NewMessage',
      sessionId,
      message: {
        content,
        messageType: 'text',
      },
    })
  }

  const startTyping = (sessionId: string) => {
    send({
      type: 'TypingStart',
      sessionId,
      userId: authStore.user?.id,
    })
  }

  const stopTyping = (sessionId: string) => {
    send({
      type: 'TypingStop',
      sessionId,
      userId: authStore.user?.id,
    })
  }

  onMounted(() => {
    if (authStore.isAuthenticated) {
      connect()
    }
  })

  onUnmounted(() => {
    ws.value?.close()
  })

  return {
    isConnected,
    connect,
    send,
    sendMessage,
    startTyping,
    stopTyping,
  }
}
```

### 5.2 组件设计

#### 5.2.1 聊天组件
```vue
<!-- components/chat/ChatWindow.vue -->
<template>
  <div class="chat-window">
    <div class="chat-header">
      <div class="customer-info">
        <h3>{{ session?.customerName || session?.customerPhone }}</h3>
        <span class="phone">{{ session?.customerPhone }}</span>
      </div>
      <div class="chat-actions">
        <el-button @click="transferChat">转移</el-button>
        <el-button @click="closeChat">结束</el-button>
      </div>
    </div>

    <div class="chat-messages" ref="messagesContainer">
      <div
        v-for="message in messages"
        :key="message.id"
        :class="['message', message.direction]"
      >
        <div class="message-content">
          <div class="text">{{ message.content }}</div>
          <div class="time">{{ formatTime(message.createdAt) }}</div>
        </div>
      </div>

      <div v-if="typingUsers.size > 0" class="typing-indicator">
        正在输入...
      </div>
    </div>

    <div class="chat-input">
      <el-input
        v-model="inputMessage"
        type="textarea"
        :rows="3"
        placeholder="输入消息..."
        @keydown.enter.exact="sendMessage"
        @input="handleTyping"
      />
      <div class="input-actions">
        <el-button @click="sendMessage" type="primary">发送</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useChatStore } from '@/stores/chat'
import { useWebSocket } from '@/composables/useWebSocket'

const props = defineProps<{
  sessionId: string
}>()

const chatStore = useChatStore()
const { sendMessage: wsSendMessage, startTyping, stopTyping } = useWebSocket()

const inputMessage = ref('')
const messagesContainer = ref<HTMLElement>()
const typingTimer = ref<number>()

const session = computed(() =>
  chatStore.sessions.find(s => s.id === props.sessionId)
)

const messages = computed(() =>
  chatStore.messages.get(props.sessionId) || []
)

const typingUsers = computed(() =>
  chatStore.typingUsers.get(props.sessionId) || new Set()
)

const sendMessage = () => {
  if (!inputMessage.value.trim()) return

  wsSendMessage(props.sessionId, inputMessage.value)
  inputMessage.value = ''
  stopTyping(props.sessionId)
}

const handleTyping = () => {
  startTyping(props.sessionId)

  // 停止输入指示器
  clearTimeout(typingTimer.value)
  typingTimer.value = setTimeout(() => {
    stopTyping(props.sessionId)
  }, 1000)
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

watch(messages, scrollToBottom, { deep: true })

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString()
}
</script>
```

#### 5.2.2 租户管理组件
```vue
<!-- views/admin/TenantManagement.vue -->
<template>
  <div class="tenant-management">
    <div class="page-header">
      <h1>租户管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        创建租户
      </el-button>
    </div>

    <div class="filters">
      <el-input
        v-model="searchQuery"
        placeholder="搜索租户..."
        style="width: 300px"
      />
      <el-select v-model="statusFilter" placeholder="状态筛选">
        <el-option label="全部" value="" />
        <el-option label="活跃" value="active" />
        <el-option label="暂停" value="suspended" />
      </el-select>
    </div>

    <el-table :data="filteredTenants" v-loading="loading">
      <el-table-column prop="name" label="租户名称" />
      <el-table-column prop="plan" label="套餐" />
      <el-table-column label="端口使用">
        <template #default="{ row }">
          {{ row.usedPorts }} / {{ row.portQuota }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="editTenant(row)">编辑</el-button>
          <el-button size="small" @click="impersonateTenant(row)">切换</el-button>
          <el-button size="small" type="danger" @click="deleteTenant(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建租户对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建租户">
      <el-form :model="createForm" :rules="createRules" ref="createFormRef">
        <el-form-item label="租户名称" prop="name">
          <el-input v-model="createForm.name" />
        </el-form-item>
        <el-form-item label="套餐" prop="plan">
          <el-select v-model="createForm.plan">
            <el-option label="基础版 (5端口)" value="basic" />
            <el-option label="专业版 (20端口)" value="pro" />
            <el-option label="企业版 (100端口)" value="enterprise" />
          </el-select>
        </el-form-item>
        <el-form-item label="管理员邮箱" prop="adminEmail">
          <el-input v-model="createForm.adminEmail" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createTenant">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTenantStore } from '@/stores/tenant'
import { useAuthStore } from '@/stores/auth'

const tenantStore = useTenantStore()
const authStore = useAuthStore()

const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const showCreateDialog = ref(false)

const createForm = ref({
  name: '',
  plan: 'basic',
  adminEmail: '',
})

const createRules = {
  name: [{ required: true, message: '请输入租户名称' }],
  plan: [{ required: true, message: '请选择套餐' }],
  adminEmail: [
    { required: true, message: '请输入管理员邮箱' },
    { type: 'email', message: '请输入有效的邮箱地址' },
  ],
}

const filteredTenants = computed(() => {
  let tenants = tenantStore.tenants

  if (searchQuery.value) {
    tenants = tenants.filter(t =>
      t.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (statusFilter.value) {
    tenants = tenants.filter(t => t.status === statusFilter.value)
  }

  return tenants
})

const loadTenants = async () => {
  loading.value = true
  try {
    await tenantStore.fetchTenants()
  } finally {
    loading.value = false
  }
}

const createTenant = async () => {
  try {
    await tenantStore.createTenant(createForm.value)
    showCreateDialog.value = false
    ElMessage.success('租户创建成功')
    loadTenants()
  } catch (error) {
    ElMessage.error('创建失败')
  }
}

const impersonateTenant = async (tenant: any) => {
  try {
    // 获取租户管理员
    const adminUser = await tenantStore.getTenantAdmin(tenant.id)
    await authStore.impersonate(adminUser.id)
    ElMessage.success(`已切换到租户: ${tenant.name}`)
  } catch (error) {
    ElMessage.error('切换失败')
  }
}

onMounted(() => {
  loadTenants()
})
</script>
```

## 6. 部署和运维

### 6.1 Docker容器化

#### 6.1.1 后端Dockerfile
```dockerfile
# Dockerfile
FROM rust:1.75 as builder

WORKDIR /app
COPY Cargo.toml Cargo.lock ./
COPY src ./src

# 构建应用
RUN cargo build --release

# 运行时镜像
FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY --from=builder /app/target/release/whatsapp-saas ./
COPY migrations ./migrations

EXPOSE 8080

CMD ["./whatsapp-saas"]
```

#### 6.1.2 前端Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

# Nginx服务
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### 6.1.3 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    container_name: whatsapp-saas-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=postgres://whatsapp:${DB_PASSWORD}@postgres:5432/whatsapp_saas
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - RUST_LOG=info
    volumes:
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
      - redis
    networks:
      - whatsapp-network

  frontend:
    build: ./frontend
    container_name: whatsapp-saas-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - whatsapp-network

  postgres:
    image: postgres:15
    container_name: whatsapp-saas-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=whatsapp_saas
      - POSTGRES_USER=whatsapp
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - whatsapp-network

  redis:
    image: redis:7-alpine
    container_name: whatsapp-saas-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - whatsapp-network

  prometheus:
    image: prom/prometheus:latest
    container_name: whatsapp-saas-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - whatsapp-network

  grafana:
    image: grafana/grafana:latest
    container_name: whatsapp-saas-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - whatsapp-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  whatsapp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 6.2 监控和日志

#### 6.2.1 Prometheus配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'whatsapp-saas'
    static_configs:
      - targets: ['app:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 6.2.2 告警规则
```yaml
# monitoring/rules/alerts.yml
groups:
  - name: whatsapp-saas-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      - alert: DatabaseConnectionFailure
        expr: up{job="postgres"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "PostgreSQL database is not responding"

      - alert: WhatsAppContainerDown
        expr: whatsapp_container_status{status!="running"} > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "WhatsApp container is down"
          description: "Container {{ $labels.container_id }} is not running"

      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Container {{ $labels.name }} memory usage is {{ $value }}%"
```

### 6.3 部署脚本

#### 6.3.1 自动化部署脚本
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

# 配置
ENVIRONMENT=${1:-production}
BACKUP_DIR="/opt/backups/whatsapp-saas"
APP_DIR="/opt/whatsapp-saas"

echo "🚀 Starting deployment for environment: $ENVIRONMENT"

# 创建备份
echo "📦 Creating backup..."
mkdir -p $BACKUP_DIR
timestamp=$(date +%Y%m%d_%H%M%S)
tar -czf "$BACKUP_DIR/backup_$timestamp.tar.gz" -C $APP_DIR . 2>/dev/null || true

# 拉取最新代码
echo "📥 Pulling latest code..."
cd $APP_DIR
git pull origin main

# 构建和部署
echo "🔨 Building and deploying..."
docker-compose -f docker-compose.yml -f docker-compose.$ENVIRONMENT.yml down
docker-compose -f docker-compose.yml -f docker-compose.$ENVIRONMENT.yml build
docker-compose -f docker-compose.yml -f docker-compose.$ENVIRONMENT.yml up -d

# 等待服务启动
echo "⏳ Waiting for services to start..."
sleep 30

# 健康检查
echo "🔍 Performing health check..."
if curl -f http://localhost:8080/health; then
    echo "✅ Deployment successful!"

    # 清理旧备份（保留最近10个）
    ls -t $BACKUP_DIR/backup_*.tar.gz | tail -n +11 | xargs rm -f 2>/dev/null || true

    echo "🎉 Deployment completed successfully!"
else
    echo "❌ Health check failed! Rolling back..."

    # 回滚
    docker-compose -f docker-compose.yml -f docker-compose.$ENVIRONMENT.yml down

    # 恢复最新备份
    latest_backup=$(ls -t $BACKUP_DIR/backup_*.tar.gz | head -n 1)
    if [ -n "$latest_backup" ]; then
        echo "📦 Restoring from backup: $latest_backup"
        tar -xzf "$latest_backup" -C $APP_DIR
        docker-compose -f docker-compose.yml -f docker-compose.$ENVIRONMENT.yml up -d
    fi

    exit 1
fi
```

#### 6.3.2 数据库迁移脚本
```bash
#!/bin/bash
# scripts/migrate.sh

set -e

DATABASE_URL=${DATABASE_URL:-"postgres://whatsapp:password@localhost:5432/whatsapp_saas"}

echo "🗄️ Running database migrations..."

# 检查数据库连接
if ! psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
    echo "❌ Cannot connect to database"
    exit 1
fi

# 运行迁移
sqlx migrate run --database-url "$DATABASE_URL"

echo "✅ Database migrations completed successfully!"
```

这个技术设计文档现在涵盖了完整的系统架构、前端设计、部署配置和运维方案，为开发团队提供了详细的技术实现指导。
