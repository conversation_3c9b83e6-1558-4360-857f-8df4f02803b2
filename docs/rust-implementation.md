# Rust 后端核心实现

## 项目依赖配置

```toml
# Cargo.toml
[package]
name = "whatsapp-saas-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web框架
axum = { version = "0.7", features = ["ws", "macros"] }
tokio = { version = "1.0", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "fs"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 数据库
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }

# 认证
jsonwebtoken = "9.0"
bcrypt = "0.15"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }

# 配置管理
config = "0.14"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 异步工具
futures = "0.3"
tokio-stream = "0.1"

# Docker API
bollard = "0.15"

# Redis
redis = { version = "0.24", features = ["tokio-comp"] }

# WebSocket
tokio-tungstenite = "0.21"

# HTTP客户端
reqwest = { version = "0.11", features = ["json"] }

# 任务调度
tokio-cron-scheduler = "0.10"
```

## 核心领域模型

```rust
// src/domain/mod.rs
pub mod tenant;
pub mod user;
pub mod whatsapp;
pub mod chat;
pub mod broadcast;
pub mod proxy;

use serde::{Deserialize, Serialize};
use uuid::Uuid;

// 通用ID类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TenantId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct UserId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct WhatsAppAccountId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ChatSessionId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct MessageId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct BroadcastTaskId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ProxyId(pub Uuid);

// 实现常用trait
macro_rules! impl_id {
    ($id_type:ident) => {
        impl $id_type {
            pub fn new() -> Self {
                Self(Uuid::new_v4())
            }
            
            pub fn from_uuid(uuid: Uuid) -> Self {
                Self(uuid)
            }
            
            pub fn as_uuid(&self) -> Uuid {
                self.0
            }
        }
        
        impl std::fmt::Display for $id_type {
            fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                write!(f, "{}", self.0)
            }
        }
        
        impl From<Uuid> for $id_type {
            fn from(uuid: Uuid) -> Self {
                Self(uuid)
            }
        }
        
        impl From<$id_type> for Uuid {
            fn from(id: $id_type) -> Self {
                id.0
            }
        }
    };
}

impl_id!(TenantId);
impl_id!(UserId);
impl_id!(WhatsAppAccountId);
impl_id!(ChatSessionId);
impl_id!(MessageId);
impl_id!(BroadcastTaskId);
impl_id!(ProxyId);
```

```rust
// src/domain/tenant.rs
use super::*;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tenant {
    pub id: TenantId,
    pub name: String,
    pub plan: SubscriptionPlan,
    pub port_quota: u16,
    pub used_ports: u16,
    pub status: TenantStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SubscriptionPlan {
    Basic { max_ports: u16 },
    Pro { max_ports: u16 },
    Enterprise { max_ports: u16 },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TenantStatus {
    Active,
    Suspended,
    Deleted,
}

impl Tenant {
    pub fn new(name: String, plan: SubscriptionPlan) -> Self {
        let now = Utc::now();
        Self {
            id: TenantId::new(),
            name,
            port_quota: plan.max_ports(),
            used_ports: 0,
            plan,
            status: TenantStatus::Active,
            created_at: now,
            updated_at: now,
        }
    }
    
    pub fn can_allocate_port(&self) -> bool {
        self.used_ports < self.port_quota && self.status == TenantStatus::Active
    }
    
    pub fn allocate_port(&mut self) -> Result<(), TenantError> {
        if !self.can_allocate_port() {
            return Err(TenantError::PortQuotaExceeded);
        }
        self.used_ports += 1;
        self.updated_at = Utc::now();
        Ok(())
    }
    
    pub fn release_port(&mut self) {
        if self.used_ports > 0 {
            self.used_ports -= 1;
            self.updated_at = Utc::now();
        }
    }
    
    pub fn upgrade_plan(&mut self, new_plan: SubscriptionPlan) {
        self.plan = new_plan;
        self.port_quota = self.plan.max_ports();
        self.updated_at = Utc::now();
    }
}

impl SubscriptionPlan {
    pub fn max_ports(&self) -> u16 {
        match self {
            Self::Basic { max_ports } => *max_ports,
            Self::Pro { max_ports } => *max_ports,
            Self::Enterprise { max_ports } => *max_ports,
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum TenantError {
    #[error("Port quota exceeded")]
    PortQuotaExceeded,
    #[error("Tenant is not active")]
    TenantNotActive,
}
```

```rust
// src/domain/user.rs
use super::*;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: UserId,
    pub tenant_id: Option<TenantId>,
    pub username: String,
    pub email: String,
    pub password_hash: String,
    pub role: UserRole,
    pub status: UserStatus,
    pub last_login: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum UserRole {
    SystemAdmin,
    TenantAdmin,
    CustomerService,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum UserStatus {
    Active,
    Inactive,
    Suspended,
}

impl User {
    pub fn new_system_admin(username: String, email: String, password_hash: String) -> Self {
        let now = Utc::now();
        Self {
            id: UserId::new(),
            tenant_id: None,
            username,
            email,
            password_hash,
            role: UserRole::SystemAdmin,
            status: UserStatus::Active,
            last_login: None,
            created_at: now,
            updated_at: now,
        }
    }
    
    pub fn new_tenant_admin(username: String, email: String, password_hash: String, tenant_id: TenantId) -> Self {
        let now = Utc::now();
        Self {
            id: UserId::new(),
            tenant_id: Some(tenant_id),
            username,
            email,
            password_hash,
            role: UserRole::TenantAdmin,
            status: UserStatus::Active,
            last_login: None,
            created_at: now,
            updated_at: now,
        }
    }
    
    pub fn new_customer_service(username: String, email: String, password_hash: String, tenant_id: TenantId) -> Self {
        let now = Utc::now();
        Self {
            id: UserId::new(),
            tenant_id: Some(tenant_id),
            username,
            email,
            password_hash,
            role: UserRole::CustomerService,
            status: UserStatus::Active,
            last_login: None,
            created_at: now,
            updated_at: now,
        }
    }
    
    pub fn can_impersonate(&self, target: &User) -> bool {
        match self.role {
            UserRole::SystemAdmin => true,
            UserRole::TenantAdmin => {
                self.tenant_id == target.tenant_id && target.role == UserRole::CustomerService
            },
            UserRole::CustomerService => false,
        }
    }
    
    pub fn update_last_login(&mut self) {
        self.last_login = Some(Utc::now());
        self.updated_at = Utc::now();
    }
}
```

```rust
// src/domain/whatsapp.rs
use super::*;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhatsAppAccount {
    pub id: WhatsAppAccountId,
    pub tenant_id: TenantId,
    pub name: String,
    pub phone_number: String,
    pub container_id: Option<String>,
    pub port: u16,
    pub proxy_id: Option<ProxyId>,
    pub status: AccountStatus,
    pub assigned_to: Option<UserId>,
    pub qr_code_path: Option<String>,
    pub webhook_url: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AccountStatus {
    Creating,
    WaitingForQR,
    Connecting,
    Connected,
    Disconnected,
    Failed,
    Deleting,
}

impl WhatsAppAccount {
    pub fn new(
        tenant_id: TenantId,
        name: String,
        phone_number: String,
        port: u16,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: WhatsAppAccountId::new(),
            tenant_id,
            name,
            phone_number,
            container_id: None,
            port,
            proxy_id: None,
            status: AccountStatus::Creating,
            assigned_to: None,
            qr_code_path: None,
            webhook_url: None,
            created_at: now,
            updated_at: now,
        }
    }
    
    pub fn set_container(&mut self, container_id: String) {
        self.container_id = Some(container_id);
        self.status = AccountStatus::WaitingForQR;
        self.updated_at = Utc::now();
    }
    
    pub fn set_proxy(&mut self, proxy_id: ProxyId) {
        self.proxy_id = Some(proxy_id);
        self.updated_at = Utc::now();
    }
    
    pub fn assign_to_agent(&mut self, agent_id: UserId) {
        self.assigned_to = Some(agent_id);
        self.updated_at = Utc::now();
    }
    
    pub fn update_status(&mut self, status: AccountStatus) {
        self.status = status;
        self.updated_at = Utc::now();
    }
    
    pub fn is_active(&self) -> bool {
        matches!(self.status, AccountStatus::Connected)
    }
}
```

```rust
// src/domain/chat.rs
use super::*;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatSession {
    pub id: ChatSessionId,
    pub whatsapp_account_id: WhatsAppAccountId,
    pub customer_phone: String,
    pub customer_name: Option<String>,
    pub assigned_agent: Option<UserId>,
    pub status: ChatStatus,
    pub priority: ChatPriority,
    pub tags: Vec<String>,
    pub metadata: serde_json::Value,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ChatStatus {
    Open,
    Assigned,
    Closed,
    Archived,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ChatPriority {
    Low,
    Normal,
    High,
    Urgent,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: MessageId,
    pub session_id: ChatSessionId,
    pub sender_id: Option<UserId>,
    pub content: String,
    pub message_type: MessageType,
    pub direction: MessageDirection,
    pub whatsapp_message_id: Option<String>,
    pub media_url: Option<String>,
    pub media_type: Option<String>,
    pub read_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageType {
    Text,
    Image,
    Audio,
    Video,
    Document,
    Location,
    Contact,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageDirection {
    Inbound,
    Outbound,
}

impl ChatSession {
    pub fn new(whatsapp_account_id: WhatsAppAccountId, customer_phone: String) -> Self {
        let now = Utc::now();
        Self {
            id: ChatSessionId::new(),
            whatsapp_account_id,
            customer_phone,
            customer_name: None,
            assigned_agent: None,
            status: ChatStatus::Open,
            priority: ChatPriority::Normal,
            tags: Vec::new(),
            metadata: serde_json::Value::Object(serde_json::Map::new()),
            created_at: now,
            updated_at: now,
        }
    }
    
    pub fn assign_to_agent(&mut self, agent_id: UserId) {
        self.assigned_agent = Some(agent_id);
        self.status = ChatStatus::Assigned;
        self.updated_at = Utc::now();
    }
    
    pub fn close(&mut self) {
        self.status = ChatStatus::Closed;
        self.updated_at = Utc::now();
    }
    
    pub fn add_tag(&mut self, tag: String) {
        if !self.tags.contains(&tag) {
            self.tags.push(tag);
            self.updated_at = Utc::now();
        }
    }
    
    pub fn set_priority(&mut self, priority: ChatPriority) {
        self.priority = priority;
        self.updated_at = Utc::now();
    }
}

impl Message {
    pub fn new_inbound(
        session_id: ChatSessionId,
        content: String,
        message_type: MessageType,
        whatsapp_message_id: Option<String>,
    ) -> Self {
        Self {
            id: MessageId::new(),
            session_id,
            sender_id: None,
            content,
            message_type,
            direction: MessageDirection::Inbound,
            whatsapp_message_id,
            media_url: None,
            media_type: None,
            read_at: None,
            created_at: Utc::now(),
        }
    }
    
    pub fn new_outbound(
        session_id: ChatSessionId,
        sender_id: UserId,
        content: String,
        message_type: MessageType,
    ) -> Self {
        Self {
            id: MessageId::new(),
            session_id,
            sender_id: Some(sender_id),
            content,
            message_type,
            direction: MessageDirection::Outbound,
            whatsapp_message_id: None,
            media_url: None,
            media_type: None,
            read_at: None,
            created_at: Utc::now(),
        }
    }
    
    pub fn mark_as_read(&mut self) {
        self.read_at = Some(Utc::now());
    }
}
```

## 应用服务层

```rust
// src/application/tenant_service.rs
use crate::domain::*;
use crate::infrastructure::repository::*;
use std::sync::Arc;
use anyhow::Result;

pub struct TenantService {
    tenant_repo: Arc<dyn TenantRepository>,
    user_repo: Arc<dyn UserRepository>,
    port_manager: Arc<PortManager>,
}

impl TenantService {
    pub fn new(
        tenant_repo: Arc<dyn TenantRepository>,
        user_repo: Arc<dyn UserRepository>,
        port_manager: Arc<PortManager>,
    ) -> Self {
        Self {
            tenant_repo,
            user_repo,
            port_manager,
        }
    }
    
    pub async fn create_tenant(&self, cmd: CreateTenantCommand) -> Result<TenantId> {
        // 1. 创建租户
        let tenant = Tenant::new(cmd.name, cmd.plan);
        let tenant_id = tenant.id;
        
        // 2. 分配端口范围
        self.port_manager.allocate_range_for_tenant(tenant_id, tenant.port_quota).await?;
        
        // 3. 创建租户管理员账号
        let password_hash = bcrypt::hash(&cmd.admin_password, bcrypt::DEFAULT_COST)?;
        let admin_user = User::new_tenant_admin(
            cmd.admin_username,
            cmd.admin_email,
            password_hash,
            tenant_id,
        );
        
        // 4. 保存数据
        self.tenant_repo.save(&tenant).await?;
        self.user_repo.save(&admin_user).await?;
        
        tracing::info!("Created tenant: {} with admin: {}", tenant_id, admin_user.id);
        
        Ok(tenant_id)
    }
    
    pub async fn get_tenant(&self, tenant_id: TenantId) -> Result<Tenant> {
        self.tenant_repo.find_by_id(tenant_id).await?
            .ok_or_else(|| anyhow::anyhow!("Tenant not found: {}", tenant_id))
    }
    
    pub async fn list_tenants(&self, page: u32, size: u32) -> Result<Vec<Tenant>> {
        self.tenant_repo.list(page, size).await
    }
    
    pub async fn upgrade_plan(&self, tenant_id: TenantId, new_plan: SubscriptionPlan) -> Result<()> {
        let mut tenant = self.get_tenant(tenant_id).await?;
        
        let old_quota = tenant.port_quota;
        let new_quota = new_plan.max_ports();
        
        // 检查是否需要更多端口
        if new_quota > old_quota {
            let additional_ports = new_quota - old_quota;
            self.port_manager.allocate_additional_ports(tenant_id, additional_ports).await?;
        } else if new_quota < old_quota && tenant.used_ports > new_quota {
            return Err(anyhow::anyhow!("Cannot downgrade: currently using {} ports, new plan allows {}", tenant.used_ports, new_quota));
        }
        
        tenant.upgrade_plan(new_plan);
        self.tenant_repo.save(&tenant).await?;
        
        tracing::info!("Upgraded tenant {} plan from {} to {} ports", tenant_id, old_quota, new_quota);
        
        Ok(())
    }
    
    pub async fn delete_tenant(&self, tenant_id: TenantId) -> Result<()> {
        // 1. 检查是否还有活跃的WhatsApp账号
        let active_accounts = self.whatsapp_service.count_active_accounts(tenant_id).await?;
        if active_accounts > 0 {
            return Err(anyhow::anyhow!("Cannot delete tenant with active WhatsApp accounts"));
        }
        
        // 2. 释放端口
        self.port_manager.release_all_ports(tenant_id).await?;
        
        // 3. 删除租户和相关用户
        self.user_repo.delete_by_tenant_id(tenant_id).await?;
        self.tenant_repo.delete(tenant_id).await?;
        
        tracing::info!("Deleted tenant: {}", tenant_id);
        
        Ok(())
    }
}

#[derive(Debug)]
pub struct CreateTenantCommand {
    pub name: String,
    pub plan: SubscriptionPlan,
    pub admin_username: String,
    pub admin_email: String,
    pub admin_password: String,
}
```

```rust
// src/application/auth_service.rs
use crate::domain::*;
use crate::infrastructure::repository::*;
use jsonwebtoken::{encode, decode, Header, Validation, EncodingKey, DecodingKey};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use anyhow::Result;
use chrono::{Duration, Utc};

pub struct AuthService {
    user_repo: Arc<dyn UserRepository>,
    jwt_secret: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,  // user_id
    pub tenant_id: Option<String>,
    pub role: UserRole,
    pub exp: i64,
    pub iat: i64,
    pub impersonated_by: Option<String>, // 如果是权限降级，记录原用户ID
}

#[derive(Debug)]
pub struct AuthToken {
    pub token: String,
    pub expires_at: chrono::DateTime<Utc>,
    pub user: User,
}

impl AuthService {
    pub fn new(user_repo: Arc<dyn UserRepository>, jwt_secret: String) -> Self {
        Self {
            user_repo,
            jwt_secret,
        }
    }
    
    pub async fn login(&self, email: String, password: String) -> Result<AuthToken> {
        let user = self.user_repo.find_by_email(&email).await?
            .ok_or_else(|| anyhow::anyhow!("Invalid credentials"))?;
        
        if user.status != UserStatus::Active {
            return Err(anyhow::anyhow!("User account is not active"));
        }
        
        if !bcrypt::verify(&password, &user.password_hash)? {
            return Err(anyhow::anyhow!("Invalid credentials"));
        }
        
        // 更新最后登录时间
        let mut user = user;
        user.update_last_login();
        self.user_repo.save(&user).await?;
        
        let token = self.generate_token(&user, None)?;
        
        tracing::info!("User logged in: {} ({})", user.email, user.id);
        
        Ok(token)
    }
    
    pub async fn impersonate(&self, admin_id: UserId, target_user_id: UserId) -> Result<AuthToken> {
        let admin = self.user_repo.find_by_id(admin_id).await?
            .ok_or_else(|| anyhow::anyhow!("Admin user not found"))?;
        
        let target = self.user_repo.find_by_id(target_user_id).await?
            .ok_or_else(|| anyhow::anyhow!("Target user not found"))?;
        
        // 验证权限
        if !admin.can_impersonate(&target) {
            return Err(anyhow::anyhow!("Insufficient permissions to impersonate user"));
        }
        
        let token = self.generate_token(&target, Some(admin_id))?;
        
        tracing::info!("User {} impersonating user {}", admin_id, target_user_id);
        
        Ok(token)
    }
    
    pub async fn verify_token(&self, token: &str) -> Result<User> {
        let claims = self.decode_token(token)?;
        let user_id = UserId::from_uuid(claims.sub.parse()?);
        
        let user = self.user_repo.find_by_id(user_id).await?
            .ok_or_else(|| anyhow::anyhow!("User not found"))?;
        
        if user.status != UserStatus::Active {
            return Err(anyhow::anyhow!("User account is not active"));
        }
        
        Ok(user)
    }
    
    fn generate_token(&self, user: &User, impersonated_by: Option<UserId>) -> Result<AuthToken> {
        let now = Utc::now();
        let expires_at = now + Duration::hours(24);
        
        let claims = Claims {
            sub: user.id.to_string(),
            tenant_id: user.tenant_id.map(|id| id.to_string()),
            role: user.role.clone(),
            exp: expires_at.timestamp(),
            iat: now.timestamp(),
            impersonated_by: impersonated_by.map(|id| id.to_string()),
        };
        
        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.jwt_secret.as_ref()),
        )?;
        
        Ok(AuthToken {
            token,
            expires_at,
            user: user.clone(),
        })
    }
    
    fn decode_token(&self, token: &str) -> Result<Claims> {
        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.jwt_secret.as_ref()),
            &Validation::default(),
        )?;
        
        Ok(token_data.claims)
    }
}

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub expires_at: chrono::DateTime<Utc>,
    pub user: UserResponse,
}

#[derive(Debug, Serialize)]
pub struct UserResponse {
    pub id: UserId,
    pub username: String,
    pub email: String,
    pub role: UserRole,
    pub tenant_id: Option<TenantId>,
}

impl From<User> for UserResponse {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            tenant_id: user.tenant_id,
        }
    }
}
```

## WhatsApp 服务实现

```rust
// src/application/whatsapp_service.rs
use crate::domain::*;
use crate::infrastructure::*;
use std::sync::Arc;
use anyhow::Result;

pub struct WhatsAppService {
    account_repo: Arc<dyn WhatsAppAccountRepository>,
    container_manager: Arc<ContainerManager>,
    proxy_pool: Arc<ProxyPool>,
    tenant_service: Arc<TenantService>,
    port_manager: Arc<PortManager>,
}

impl WhatsAppService {
    pub fn new(
        account_repo: Arc<dyn WhatsAppAccountRepository>,
        container_manager: Arc<ContainerManager>,
        proxy_pool: Arc<ProxyPool>,
        tenant_service: Arc<TenantService>,
        port_manager: Arc<PortManager>,
    ) -> Self {
        Self {
            account_repo,
            container_manager,
            proxy_pool,
            tenant_service,
            port_manager,
        }
    }

    pub async fn create_account(&self, cmd: CreateWhatsAppAccountCommand) -> Result<WhatsAppAccountId> {
        // 1. 检查租户端口配额
        let mut tenant = self.tenant_service.get_tenant(cmd.tenant_id).await?;
        if !tenant.can_allocate_port() {
            return Err(anyhow::anyhow!("Port quota exceeded for tenant"));
        }

        // 2. 分配端口
        let port = self.port_manager.allocate_for_tenant(cmd.tenant_id).await?;

        // 3. 创建账号记录
        let mut account = WhatsAppAccount::new(
            cmd.tenant_id,
            cmd.name,
            cmd.phone_number,
            port,
        );

        // 4. 分配代理（如果需要）
        if let Some(proxy_req) = cmd.proxy_requirements {
            let proxy = self.proxy_pool.allocate_proxy(account.id, proxy_req).await?;
            account.set_proxy(proxy.id);
        }

        // 5. 创建容器
        let container_id = self.container_manager.create_whatsapp_container(&account).await?;
        account.set_container(container_id);

        // 6. 更新租户端口使用量
        tenant.allocate_port()?;
        self.tenant_service.save_tenant(&tenant).await?;

        // 7. 保存账号
        self.account_repo.save(&account).await?;

        tracing::info!("Created WhatsApp account: {} for tenant: {}", account.id, cmd.tenant_id);

        Ok(account.id)
    }

    pub async fn delete_account(&self, account_id: WhatsAppAccountId) -> Result<()> {
        let account = self.get_account(account_id).await?;

        // 1. 停止并删除容器
        if let Some(container_id) = &account.container_id {
            self.container_manager.remove_container(container_id).await?;
        }

        // 2. 释放代理
        if account.proxy_id.is_some() {
            self.proxy_pool.release_proxy(account_id).await?;
        }

        // 3. 释放端口
        self.port_manager.release_port(account.port).await?;

        // 4. 更新租户端口使用量
        let mut tenant = self.tenant_service.get_tenant(account.tenant_id).await?;
        tenant.release_port();
        self.tenant_service.save_tenant(&tenant).await?;

        // 5. 删除账号记录
        self.account_repo.delete(account_id).await?;

        tracing::info!("Deleted WhatsApp account: {}", account_id);

        Ok(())
    }

    pub async fn get_account(&self, account_id: WhatsAppAccountId) -> Result<WhatsAppAccount> {
        self.account_repo.find_by_id(account_id).await?
            .ok_or_else(|| anyhow::anyhow!("WhatsApp account not found: {}", account_id))
    }

    pub async fn list_accounts_by_tenant(&self, tenant_id: TenantId) -> Result<Vec<WhatsAppAccount>> {
        self.account_repo.find_by_tenant_id(tenant_id).await
    }

    pub async fn assign_to_agent(&self, account_id: WhatsAppAccountId, agent_id: UserId) -> Result<()> {
        let mut account = self.get_account(account_id).await?;
        account.assign_to_agent(agent_id);
        self.account_repo.save(&account).await?;

        tracing::info!("Assigned WhatsApp account {} to agent {}", account_id, agent_id);

        Ok(())
    }

    pub async fn get_qr_code(&self, account_id: WhatsAppAccountId) -> Result<Option<String>> {
        let account = self.get_account(account_id).await?;

        if let Some(container_id) = &account.container_id {
            // 从容器获取二维码
            self.container_manager.get_qr_code(container_id).await
        } else {
            Ok(None)
        }
    }

    pub async fn update_account_status(&self, account_id: WhatsAppAccountId, status: AccountStatus) -> Result<()> {
        let mut account = self.get_account(account_id).await?;
        account.update_status(status);
        self.account_repo.save(&account).await?;

        tracing::info!("Updated WhatsApp account {} status to {:?}", account_id, status);

        Ok(())
    }
}

#[derive(Debug)]
pub struct CreateWhatsAppAccountCommand {
    pub tenant_id: TenantId,
    pub name: String,
    pub phone_number: String,
    pub proxy_requirements: Option<ProxyRequirements>,
}

#[derive(Debug)]
pub struct ProxyRequirements {
    pub country: Option<String>,
    pub region: Option<String>,
    pub max_latency_ms: Option<u64>,
    pub min_success_rate: Option<f64>,
}
```

## 聊天服务实现

```rust
// src/application/chat_service.rs
use crate::domain::*;
use crate::infrastructure::*;
use std::sync::Arc;
use anyhow::Result;

pub struct ChatService {
    session_repo: Arc<dyn ChatSessionRepository>,
    message_repo: Arc<dyn MessageRepository>,
    whatsapp_service: Arc<WhatsAppService>,
    websocket_manager: Arc<WebSocketManager>,
    notification_service: Arc<NotificationService>,
}

impl ChatService {
    pub fn new(
        session_repo: Arc<dyn ChatSessionRepository>,
        message_repo: Arc<dyn MessageRepository>,
        whatsapp_service: Arc<WhatsAppService>,
        websocket_manager: Arc<WebSocketManager>,
        notification_service: Arc<NotificationService>,
    ) -> Self {
        Self {
            session_repo,
            message_repo,
            whatsapp_service,
            websocket_manager,
            notification_service,
        }
    }

    pub async fn handle_incoming_message(&self, webhook_data: WhatsAppWebhookData) -> Result<()> {
        // 1. 查找或创建聊天会话
        let session = self.find_or_create_session(
            webhook_data.whatsapp_account_id,
            webhook_data.from_phone.clone(),
        ).await?;

        // 2. 创建消息记录
        let message = Message::new_inbound(
            session.id,
            webhook_data.message_content,
            webhook_data.message_type,
            Some(webhook_data.whatsapp_message_id),
        );

        self.message_repo.save(&message).await?;

        // 3. 通知相关客服
        if let Some(agent_id) = session.assigned_agent {
            self.websocket_manager.send_to_user(
                agent_id,
                WebSocketMessage::NewMessage {
                    session_id: session.id,
                    message: message.clone(),
                },
            ).await?;
        } else {
            // 如果没有分配客服，通知所有可用客服
            self.notification_service.notify_available_agents(
                session.whatsapp_account_id,
                &session,
                &message,
            ).await?;
        }

        tracing::info!("Handled incoming message for session: {}", session.id);

        Ok(())
    }

    pub async fn send_message(&self, cmd: SendMessageCommand) -> Result<MessageId> {
        // 1. 验证权限
        let session = self.get_session(cmd.session_id).await?;
        self.verify_agent_permission(cmd.agent_id, &session).await?;

        // 2. 获取WhatsApp账号
        let wa_account = self.whatsapp_service.get_account(session.whatsapp_account_id).await?;

        // 3. 通过容器发送消息
        let whatsapp_message_id = self.send_via_container(&wa_account, &cmd).await?;

        // 4. 创建消息记录
        let message = Message::new_outbound(
            cmd.session_id,
            cmd.agent_id,
            cmd.content,
            cmd.message_type,
        );

        self.message_repo.save(&message).await?;

        // 5. 通知其他相关用户
        self.websocket_manager.broadcast_to_session(
            cmd.session_id,
            WebSocketMessage::MessageSent {
                message: message.clone(),
            },
        ).await?;

        tracing::info!("Sent message from agent {} in session {}", cmd.agent_id, cmd.session_id);

        Ok(message.id)
    }

    pub async fn assign_chat_to_agent(&self, session_id: ChatSessionId, agent_id: UserId) -> Result<()> {
        let mut session = self.get_session(session_id).await?;

        // 验证客服权限
        self.verify_agent_tenant_access(agent_id, session.whatsapp_account_id).await?;

        session.assign_to_agent(agent_id);
        self.session_repo.save(&session).await?;

        // 通知客服
        self.websocket_manager.send_to_user(
            agent_id,
            WebSocketMessage::ChatAssigned {
                session: session.clone(),
            },
        ).await?;

        tracing::info!("Assigned chat session {} to agent {}", session_id, agent_id);

        Ok(())
    }

    pub async fn close_chat(&self, session_id: ChatSessionId, agent_id: UserId) -> Result<()> {
        let mut session = self.get_session(session_id).await?;

        // 验证权限
        self.verify_agent_permission(agent_id, &session).await?;

        session.close();
        self.session_repo.save(&session).await?;

        // 通知相关用户
        self.websocket_manager.broadcast_to_session(
            session_id,
            WebSocketMessage::ChatClosed {
                session_id,
                closed_by: agent_id,
            },
        ).await?;

        tracing::info!("Closed chat session {} by agent {}", session_id, agent_id);

        Ok(())
    }

    pub async fn get_agent_chats(&self, agent_id: UserId) -> Result<Vec<ChatSession>> {
        self.session_repo.find_by_agent_id(agent_id).await
    }

    pub async fn get_chat_messages(&self, session_id: ChatSessionId, page: u32, size: u32) -> Result<Vec<Message>> {
        self.message_repo.find_by_session_id(session_id, page, size).await
    }

    async fn find_or_create_session(
        &self,
        whatsapp_account_id: WhatsAppAccountId,
        customer_phone: String,
    ) -> Result<ChatSession> {
        // 查找现有的开放会话
        if let Some(session) = self.session_repo
            .find_open_session(whatsapp_account_id, &customer_phone)
            .await?
        {
            return Ok(session);
        }

        // 创建新会话
        let session = ChatSession::new(whatsapp_account_id, customer_phone);
        self.session_repo.save(&session).await?;

        Ok(session)
    }

    async fn verify_agent_permission(&self, agent_id: UserId, session: &ChatSession) -> Result<()> {
        // 检查客服是否有权限访问这个会话
        if let Some(assigned_agent) = session.assigned_agent {
            if assigned_agent != agent_id {
                return Err(anyhow::anyhow!("Agent not assigned to this chat"));
            }
        }

        // 验证客服是否属于正确的租户
        self.verify_agent_tenant_access(agent_id, session.whatsapp_account_id).await
    }

    async fn verify_agent_tenant_access(&self, agent_id: UserId, whatsapp_account_id: WhatsAppAccountId) -> Result<()> {
        let wa_account = self.whatsapp_service.get_account(whatsapp_account_id).await?;
        // 这里需要验证agent_id是否属于wa_account.tenant_id
        // 具体实现需要查询用户表
        Ok(())
    }

    async fn send_via_container(&self, wa_account: &WhatsAppAccount, cmd: &SendMessageCommand) -> Result<String> {
        // 通过HTTP请求发送到WhatsApp容器
        let client = reqwest::Client::new();
        let url = format!("http://localhost:{}/send/message", wa_account.port);

        let payload = serde_json::json!({
            "phone": cmd.target_phone,
            "message": cmd.content,
            "type": cmd.message_type
        });

        let response = client
            .post(&url)
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Failed to send message via WhatsApp container"));
        }

        let result: serde_json::Value = response.json().await?;
        let message_id = result["message_id"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("No message_id in response"))?;

        Ok(message_id.to_string())
    }

    async fn get_session(&self, session_id: ChatSessionId) -> Result<ChatSession> {
        self.session_repo.find_by_id(session_id).await?
            .ok_or_else(|| anyhow::anyhow!("Chat session not found: {}", session_id))
    }
}

#[derive(Debug)]
pub struct SendMessageCommand {
    pub session_id: ChatSessionId,
    pub agent_id: UserId,
    pub content: String,
    pub message_type: MessageType,
    pub target_phone: String,
}

#[derive(Debug)]
pub struct WhatsAppWebhookData {
    pub whatsapp_account_id: WhatsAppAccountId,
    pub whatsapp_message_id: String,
    pub from_phone: String,
    pub message_content: String,
    pub message_type: MessageType,
}
```

## 基础设施层 - 数据库仓储

```rust
// src/infrastructure/repository/mod.rs
pub mod tenant_repository;
pub mod user_repository;
pub mod whatsapp_repository;
pub mod chat_repository;

use crate::domain::*;
use async_trait::async_trait;
use anyhow::Result;

#[async_trait]
pub trait TenantRepository: Send + Sync {
    async fn save(&self, tenant: &Tenant) -> Result<()>;
    async fn find_by_id(&self, id: TenantId) -> Result<Option<Tenant>>;
    async fn list(&self, page: u32, size: u32) -> Result<Vec<Tenant>>;
    async fn delete(&self, id: TenantId) -> Result<()>;
}

#[async_trait]
pub trait UserRepository: Send + Sync {
    async fn save(&self, user: &User) -> Result<()>;
    async fn find_by_id(&self, id: UserId) -> Result<Option<User>>;
    async fn find_by_email(&self, email: &str) -> Result<Option<User>>;
    async fn find_by_tenant_id(&self, tenant_id: TenantId) -> Result<Vec<User>>;
    async fn delete(&self, id: UserId) -> Result<()>;
    async fn delete_by_tenant_id(&self, tenant_id: TenantId) -> Result<()>;
}

#[async_trait]
pub trait WhatsAppAccountRepository: Send + Sync {
    async fn save(&self, account: &WhatsAppAccount) -> Result<()>;
    async fn find_by_id(&self, id: WhatsAppAccountId) -> Result<Option<WhatsAppAccount>>;
    async fn find_by_tenant_id(&self, tenant_id: TenantId) -> Result<Vec<WhatsAppAccount>>;
    async fn delete(&self, id: WhatsAppAccountId) -> Result<()>;
}

#[async_trait]
pub trait ChatSessionRepository: Send + Sync {
    async fn save(&self, session: &ChatSession) -> Result<()>;
    async fn find_by_id(&self, id: ChatSessionId) -> Result<Option<ChatSession>>;
    async fn find_by_agent_id(&self, agent_id: UserId) -> Result<Vec<ChatSession>>;
    async fn find_open_session(&self, whatsapp_account_id: WhatsAppAccountId, customer_phone: &str) -> Result<Option<ChatSession>>;
}

#[async_trait]
pub trait MessageRepository: Send + Sync {
    async fn save(&self, message: &Message) -> Result<()>;
    async fn find_by_session_id(&self, session_id: ChatSessionId, page: u32, size: u32) -> Result<Vec<Message>>;
}
```

这个实现提供了：

1. **完整的领域模型** - 类型安全的ID系统和业务实体
2. **清晰的错误处理** - 使用thiserror和anyhow进行错误管理
3. **认证授权系统** - JWT令牌和权限降级支持
4. **租户管理服务** - 完整的多租户生命周期管理
5. **WhatsApp服务** - 账号创建、容器管理、代理分配
6. **聊天服务** - 实时消息处理、会话管理、权限验证
7. **异步架构** - 基于Tokio的高性能异步处理
8. **仓储模式** - 清晰的数据访问抽象层

接下来我会继续添加更多的服务实现和基础设施层代码。
