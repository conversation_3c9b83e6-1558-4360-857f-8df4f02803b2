# WhatsApp SaaS 多租户系统设计文档

## 项目概述

基于 Rust + Vue.js 构建的多租户 WhatsApp 客服管理 SaaS 系统，支持租户管理、客服工作台、群发营销等功能。

## 系统特性

### 🏢 多租户架构
- **租户隔离**：数据和资源完全隔离
- **端口配额**：基于订阅计划的端口数量限制
- **弹性扩容**：支持租户计划升级和端口扩容

### 👥 角色权限系统
- **系统管理员**：管理所有租户、代理池、系统配置
- **租户主管**：管理 WhatsApp 账号、客服人员、群发任务
- **客服人员**：处理客户聊天、发送消息

### 💬 实时聊天功能
- **多账号管理**：每个租户可创建多个 WhatsApp 账号
- **聊天分配**：自动或手动分配聊天给客服
- **实时通信**：WebSocket 实时消息推送

### 📢 群发营销
- **定时群发**：支持定时发送营销消息
- **模板管理**：消息模板和目标群组管理
- **批量处理**：高效的批量消息发送

### 🔄 权限降级
- **系统管理员**：可切换到任意租户进行管理
- **租户主管**：可降级进入客服台处理聊天

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Vue.js 前端                               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   系统管理台     │ │   租户管理台     │ │   客服工作台     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  Rust 后端服务                               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   认证授权       │ │   租户管理       │ │   聊天服务       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   WA账号管理     │ │   代理池管理     │ │   群发服务       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  容器层                                      │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  WA Container1  │ │  WA Container2  │ │  WA Container3  │ │
│  │  (Tenant A)     │ │  (Tenant A)     │ │  (Tenant B)     │ │
│  │  Port: 3001     │ │  Port: 3002     │ │  Port: 3003     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心领域模型

### 租户模型
```rust
#[derive(Debug, Clone)]
pub struct Tenant {
    pub id: TenantId,
    pub name: String,
    pub plan: SubscriptionPlan,
    pub port_quota: u16,        // 可用端口数量
    pub used_ports: u16,        // 已使用端口数量
    pub status: TenantStatus,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub enum SubscriptionPlan {
    Basic { max_ports: u16 },
    Pro { max_ports: u16 },
    Enterprise { max_ports: u16 },
}
```

### 用户角色模型
```rust
#[derive(Debug, Clone)]
pub struct User {
    pub id: UserId,
    pub tenant_id: Option<TenantId>,
    pub username: String,
    pub email: String,
    pub role: UserRole,
    pub permissions: Vec<Permission>,
}

#[derive(Debug, Clone)]
pub enum UserRole {
    SystemAdmin,                // 系统管理员
    TenantAdmin,               // 租户主管
    CustomerService,           // 客服
}
```

### WhatsApp账号模型
```rust
#[derive(Debug, Clone)]
pub struct WhatsAppAccount {
    pub id: WhatsAppAccountId,
    pub tenant_id: TenantId,
    pub name: String,
    pub phone_number: String,
    pub container_id: Option<String>,
    pub port: u16,
    pub proxy_id: Option<ProxyId>,
    pub status: AccountStatus,
    pub assigned_to: Option<UserId>, // 分配给哪个客服
}
```

### 聊天会话模型
```rust
#[derive(Debug, Clone)]
pub struct ChatSession {
    pub id: ChatSessionId,
    pub whatsapp_account_id: WhatsAppAccountId,
    pub customer_phone: String,
    pub assigned_agent: Option<UserId>,
    pub status: ChatStatus,
    pub created_at: DateTime<Utc>,
}
```

### 群发任务模型
```rust
#[derive(Debug, Clone)]
pub struct BroadcastTask {
    pub id: BroadcastTaskId,
    pub tenant_id: TenantId,
    pub name: String,
    pub message_template: String,
    pub target_groups: Vec<String>,
    pub schedule_time: Option<DateTime<Utc>>,
    pub status: BroadcastStatus,
    pub created_by: UserId,
}
```

## 技术栈

### 后端技术栈
- **框架**: Axum + Tokio (异步 Rust Web 框架)
- **数据库**: PostgreSQL (主数据库) + Redis (缓存/会话)
- **容器管理**: Bollard (Docker API 客户端)
- **实时通信**: WebSocket (tokio-tungstenite)
- **任务调度**: Tokio-cron-scheduler
- **认证**: JWT + bcrypt

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus / Ant Design Vue
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **实时通信**: WebSocket API

### 基础设施
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **监控**: Prometheus + Grafana
- **日志**: 结构化日志 (tracing)

## 项目结构

```
whatsapp-saas/
├── backend/                    # Rust 后端
│   ├── src/
│   │   ├── main.rs
│   │   ├── lib.rs
│   │   ├── domain/             # 领域层
│   │   │   ├── tenant/
│   │   │   ├── user/
│   │   │   ├── whatsapp/
│   │   │   ├── chat/
│   │   │   └── broadcast/
│   │   ├── application/        # 应用层
│   │   │   ├── tenant_service.rs
│   │   │   ├── auth_service.rs
│   │   │   ├── whatsapp_service.rs
│   │   │   ├── chat_service.rs
│   │   │   └── broadcast_service.rs
│   │   ├── infrastructure/     # 基础设施层
│   │   │   ├── database/
│   │   │   ├── container/
│   │   │   ├── proxy/
│   │   │   └── websocket/
│   │   ├── interface/          # 接口层
│   │   │   ├── http/
│   │   │   ├── websocket/
│   │   │   └── grpc/
│   │   └── shared/             # 共享组件
│   │       ├── config.rs
│   │       ├── errors.rs
│   │       └── events.rs
│   ├── Cargo.toml
│   └── Dockerfile
├── frontend/                   # Vue 前端
│   ├── src/
│   │   ├── main.ts
│   │   ├── App.vue
│   │   ├── views/              # 页面组件
│   │   │   ├── admin/          # 系统管理员页面
│   │   │   ├── tenant/         # 租户管理页面
│   │   │   └── agent/          # 客服工作台页面
│   │   ├── components/         # 通用组件
│   │   ├── stores/             # Pinia 状态管理
│   │   ├── router/             # 路由配置
│   │   ├── api/                # API 接口
│   │   └── utils/              # 工具函数
│   ├── package.json
│   └── Dockerfile
├── deployments/                # 部署配置
│   ├── docker-compose.yml
│   ├── nginx.conf
│   └── k8s/                    # Kubernetes 配置
├── docs/                       # 文档
└── scripts/                    # 脚本工具
```

## 数据库设计

### 核心表结构

```sql
-- 租户表
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    plan VARCHAR(50) NOT NULL,
    port_quota INTEGER NOT NULL,
    used_ports INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'active',
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- WhatsApp账号表
CREATE TABLE whatsapp_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(50) NOT NULL,
    container_id VARCHAR(255),
    port INTEGER NOT NULL,
    proxy_id UUID REFERENCES proxies(id),
    status VARCHAR(50) DEFAULT 'creating',
    assigned_to UUID REFERENCES users(id),
    qr_code_path VARCHAR(500),
    webhook_url VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 代理表
CREATE TABLE proxies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    username VARCHAR(255),
    password VARCHAR(255),
    country VARCHAR(10),
    region VARCHAR(50),
    status VARCHAR(50) NOT NULL DEFAULT 'inactive',
    last_check TIMESTAMP WITH TIME ZONE,
    latency_ms INTEGER DEFAULT 0,
    success_rate DECIMAL(5,4) DEFAULT 0.0,
    in_use BOOLEAN DEFAULT FALSE,
    assigned_to UUID REFERENCES whatsapp_accounts(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 聊天会话表
CREATE TABLE chat_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    whatsapp_account_id UUID NOT NULL REFERENCES whatsapp_accounts(id),
    customer_phone VARCHAR(50) NOT NULL,
    customer_name VARCHAR(255),
    assigned_agent UUID REFERENCES users(id),
    status VARCHAR(50) DEFAULT 'open',
    priority VARCHAR(20) DEFAULT 'normal',
    tags JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 消息表
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES chat_sessions(id),
    sender_id UUID REFERENCES users(id),
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text',
    direction VARCHAR(20) NOT NULL, -- 'inbound' or 'outbound'
    whatsapp_message_id VARCHAR(255),
    media_url VARCHAR(500),
    media_type VARCHAR(100),
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 群发任务表
CREATE TABLE broadcast_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    name VARCHAR(255) NOT NULL,
    message_template TEXT NOT NULL,
    target_groups JSONB NOT NULL,
    whatsapp_accounts JSONB NOT NULL, -- 使用的WA账号列表
    schedule_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'pending',
    total_targets INTEGER DEFAULT 0,
    sent_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    executed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 群发记录表
CREATE TABLE broadcast_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES broadcast_tasks(id),
    whatsapp_account_id UUID NOT NULL REFERENCES whatsapp_accounts(id),
    target_phone VARCHAR(50) NOT NULL,
    message_content TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    whatsapp_message_id VARCHAR(255),
    error_message TEXT,
    sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 端口分配表
CREATE TABLE port_allocations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    port_number INTEGER NOT NULL UNIQUE,
    whatsapp_account_id UUID REFERENCES whatsapp_accounts(id),
    status VARCHAR(50) DEFAULT 'allocated', -- allocated, in_use, released
    allocated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    released_at TIMESTAMP WITH TIME ZONE
);
```

### 索引设计

```sql
-- 性能优化索引
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_whatsapp_accounts_tenant_id ON whatsapp_accounts(tenant_id);
CREATE INDEX idx_whatsapp_accounts_status ON whatsapp_accounts(status);
CREATE INDEX idx_chat_sessions_wa_account ON chat_sessions(whatsapp_account_id);
CREATE INDEX idx_chat_sessions_agent ON chat_sessions(assigned_agent);
CREATE INDEX idx_chat_sessions_status ON chat_sessions(status);
CREATE INDEX idx_messages_session_id ON messages(session_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_broadcast_tasks_tenant_id ON broadcast_tasks(tenant_id);
CREATE INDEX idx_broadcast_tasks_status ON broadcast_tasks(status);
CREATE INDEX idx_broadcast_records_task_id ON broadcast_records(task_id);
CREATE INDEX idx_port_allocations_tenant_id ON port_allocations(tenant_id);
CREATE INDEX idx_port_allocations_status ON port_allocations(status);
```

## API 接口设计

### 认证相关
```
POST   /api/auth/login              # 用户登录
POST   /api/auth/logout             # 用户登出
POST   /api/auth/refresh            # 刷新令牌
POST   /api/auth/impersonate        # 权限降级
GET    /api/auth/me                 # 获取当前用户信息
```

### 系统管理员接口
```
GET    /api/admin/tenants           # 获取租户列表
POST   /api/admin/tenants           # 创建租户
GET    /api/admin/tenants/:id       # 获取租户详情
PUT    /api/admin/tenants/:id       # 更新租户
DELETE /api/admin/tenants/:id       # 删除租户

GET    /api/admin/proxies           # 获取代理列表
POST   /api/admin/proxies           # 添加代理
DELETE /api/admin/proxies/:id       # 删除代理
POST   /api/admin/proxies/:id/test  # 测试代理

GET    /api/admin/stats             # 系统统计信息
```

### 租户管理接口
```
GET    /api/tenant/dashboard        # 租户仪表板
GET    /api/tenant/whatsapp-accounts # 获取WA账号列表
POST   /api/tenant/whatsapp-accounts # 创建WA账号
DELETE /api/tenant/whatsapp-accounts/:id # 删除WA账号

GET    /api/tenant/agents           # 获取客服列表
POST   /api/tenant/agents           # 创建客服账号
PUT    /api/tenant/agents/:id       # 更新客服信息

GET    /api/tenant/broadcast-tasks  # 获取群发任务
POST   /api/tenant/broadcast-tasks  # 创建群发任务
POST   /api/tenant/broadcast-tasks/:id/execute # 执行群发

GET    /api/tenant/chat-monitor     # 聊天监控
```

### 客服工作台接口
```
GET    /api/agent/chats             # 获取我的聊天列表
GET    /api/agent/chats/:id/messages # 获取聊天消息
POST   /api/agent/chats/:id/messages # 发送消息
POST   /api/agent/chats/:id/assign  # 接受聊天分配
POST   /api/agent/chats/:id/close   # 关闭聊天

GET    /api/agent/stats             # 客服统计信息
```

### WhatsApp API 代理
```
# 代理到对应的 WhatsApp 容器
/whatsapp/:account_id/*             # 代理所有 WhatsApp API 请求
```

### WebSocket 接口
```
/ws                                 # WebSocket 连接端点
```

## 前端页面设计

### 系统管理员页面
- **租户管理**: 创建、编辑、删除租户，查看租户使用情况
- **代理池管理**: 添加、测试、删除代理服务器
- **系统监控**: 查看系统整体运行状态和统计信息
- **用户切换**: 切换到任意租户进行管理

### 租户管理页面
- **仪表板**: 租户概览、使用统计、快速操作
- **WhatsApp账号管理**: 创建、配置、监控WA账号
- **客服管理**: 添加客服、分配权限、查看工作状态
- **群发管理**: 创建群发任务、设置定时发送、查看发送报告
- **聊天监控**: 实时监控所有聊天会话、客服工作状态

### 客服工作台页面
- **聊天列表**: 显示分配给我的聊天会话
- **聊天窗口**: 实时聊天界面、消息发送、文件传输
- **客户信息**: 客户资料、聊天历史、标签管理
- **快捷回复**: 常用回复模板、快速操作

## 部署方案

### 单服务器部署 (推荐)

```yaml
# docker-compose.yml
version: '3.8'

services:
  whatsapp-saas-backend:
    build: ./backend
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**********************************/whatsapp_saas
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret
    volumes:
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
      - redis

  whatsapp-saas-frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - whatsapp-saas-backend

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=whatsapp_saas
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - whatsapp-saas-frontend
      - whatsapp-saas-backend

volumes:
  postgres_data:
  redis_data:
```

### 环境配置

```bash
# .env
DATABASE_URL=postgres://user:pass@localhost:5432/whatsapp_saas
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
LOG_LEVEL=info
WHATSAPP_CONTAINER_IMAGE=aldinokemal2104/go-whatsapp-web-multidevice
PORT_RANGE_START=3001
PORT_RANGE_END=4000
```

## 安全考虑

### 数据安全
- **数据加密**: 敏感数据加密存储
- **租户隔离**: 严格的数据访问控制
- **备份策略**: 定期数据备份和恢复测试

### 网络安全
- **HTTPS**: 强制使用 HTTPS 连接
- **CORS**: 配置跨域访问策略
- **限流**: API 请求频率限制
- **防火墙**: 容器网络隔离

### 认证授权
- **JWT**: 无状态的令牌认证
- **角色权限**: 基于角色的访问控制
- **会话管理**: 安全的会话生命周期管理
- **密码策略**: 强密码要求和定期更换

## 监控和运维

### 监控指标
- **系统指标**: CPU、内存、磁盘、网络
- **应用指标**: 请求量、响应时间、错误率
- **业务指标**: 租户数量、活跃用户、消息量

### 日志管理
- **结构化日志**: JSON 格式的日志输出
- **日志级别**: 支持不同级别的日志记录
- **日志聚合**: 集中收集和分析日志

### 告警机制
- **系统告警**: 资源使用率、服务状态
- **业务告警**: 异常行为、性能问题
- **通知方式**: 邮件、短信、Webhook

## 开发指南

### 开发环境搭建

```bash
# 克隆项目
git clone https://github.com/your-org/whatsapp-saas.git
cd whatsapp-saas

# 启动后端开发环境
cd backend
cargo run

# 启动前端开发环境
cd frontend
npm install
npm run dev

# 启动数据库
docker-compose up postgres redis
```

### 代码规范
- **Rust**: 使用 rustfmt 和 clippy
- **Vue**: 使用 ESLint 和 Prettier
- **Git**: 使用 Conventional Commits 规范

### 测试策略
- **单元测试**: 核心业务逻辑测试
- **集成测试**: API 接口测试
- **端到端测试**: 完整业务流程测试

这个设计提供了一个完整的多租户 WhatsApp SaaS 系统架构，具有良好的可扩展性和维护性。
