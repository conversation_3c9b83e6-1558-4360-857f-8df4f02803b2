---
description: complete guide about project structure
globs: 
alwaysApply: false
---
# Go WhatsApp Web Multidevice API

This is a Go implementation of a WhatsApp Web Multidevice API that allows you to interact with WhatsApp through HTTP APIs.

## Project Structure

### Root Directory

- [readme.md](mdc:readme.md) - Project documentation and usage instructions
- [docker-compose.yml](mdc:docker-compose.yml) - Docker configuration for running the application
- [LICENCE.txt](mdc:LICENCE.txt) - License information

### Source Code (`src/`)

The main source code is organized in the `src` directory with the following structure:

#### Command Line Interface

- [src/cmd/root.go](mdc:src/cmd/root.go) - Root command using Cobra for CLI commands, handles configuration loading
- [src/cmd/rest.go](mdc:src/cmd/rest.go) - REST server command implementation
- [src/cmd/mcp.go](mdc:src/cmd/mcp.go) - Model Context Protocol server command implementation

#### Configuration

- [src/config/](mdc:src/config) - Application configuration settings and constants

#### Domain Models

The application is organized using domain-driven design principles:

- [src/domains/app/](mdc:src/domains/app) - Core application domain models
- [src/domains/chat/](mdc:src/domains/chat) - Chat-related domain models and interfaces
- [src/domains/group/](mdc:src/domains/group) - Group-related domain models
- [src/domains/message/](mdc:src/domains/message) - Message-related domain models
- [src/domains/newsletter/](mdc:src/domains/newsletter) - Newsletter-related domain models
- [src/domains/send/](mdc:src/domains/send) - Message sending domain models
- [src/domains/user/](mdc:src/domains/user) - User-related domain models

#### Infrastructure

- [src/infrastructure/chatstorage/](mdc:src/infrastructure/chatstorage) - Chat storage repository and WhatsApp integration
- [src/infrastructure/whatsapp/](mdc:src/infrastructure/whatsapp) - WhatsApp client implementation and related infrastructure

#### User Interface

- [src/ui/rest/](mdc:src/ui/rest) - REST API implementation
  - [src/ui/rest/helpers/](mdc:src/ui/rest/helpers) - Helper functions for REST handlers
  - [src/ui/rest/middleware/](mdc:src/ui/rest/middleware) - Middleware components for request processing
- [src/ui/websocket/](mdc:src/ui/websocket) - WebSocket implementation for real-time communication
- [src/ui/mcp/](mdc:src/ui/mcp) - Model Context Protocol server to communication with AI Agent

#### Utilities and Shared Components

- [src/pkg/error/](mdc:src/pkg/error) - Error handling utilities
- [src/pkg/utils/](mdc:src/pkg/utils) - General utility functions

#### Use Cases

- [src/usecase/](mdc:src/usecase) - Application services that implement business logic

#### Static Resources

- [src/statics/](mdc:src/statics) - Static resources like media files
  - [src/statics/media/](mdc:src/statics/media) - Media files
  - [src/statics/qrcode/](mdc:src/statics/qrcode) - QR code images for WhatsApp authentication
  - [src/statics/senditems/](mdc:src/statics/senditems) - Items to be sent via WhatsApp

#### Validation

- [src/validations/](mdc:src/validations) - Request validation logic

#### Views

- [src/views/](mdc:src/views) - Templates and UI components
  - [src/views/assets/](mdc:src/views/assets) - Frontend assets (CSS, JS, etc.)
  - [src/views/components/](mdc:src/views/components) - Reusable UI components
    - [src/views/components/generic/](mdc:src/views/components/generic) - Generic UI components

### Documentation

- [docs/](mdc:docs) - Project documentation
  - [docs/openapi.yaml](mdc:docs/openapi.yaml) - OpenAPI specification for the REST API
  - [docs/sdk/](mdc:docs/sdk) - SDK documentation

### Docker

- [docker/](mdc:docker) - Docker-related files and configurations
  - [docker/golang.Dockerfile](mdc:docker/golang.Dockerfile) - Dockerfile for building the Go application

## Key Application Features

- WhatsApp login via QR code or pairing code
- Send/receive messages, media, contacts, locations
- Group management features
- Newsletter management
- WebSocket real-time updates
- Webhooks for message events
- Auto-reply functionality
- Model Context Protocol (MCP) server for AI agent communication

## Application Flow

### REST Server Mode

1. The application starts from [src/cmd/root.go](mdc:src/cmd/root.go)
2. REST server command is executed via [src/cmd/rest.go](mdc:src/cmd/rest.go)
3. Configuration is loaded from environment variables or command line flags
4. The REST server is initialized using Fiber framework
5. WhatsApp client is initialized and services are created
6. REST routes are registered for different domains
7. WebSocket hub is started for real-time communication
8. Background tasks are started (auto-reconnect, chat storage flushing)
9. The server listens for requests on the configured port

### MCP Server Mode

1. The application starts from [src/cmd/root.go](mdc:src/cmd/root.go)
2. MCP server command is executed via [src/cmd/mcp.go](mdc:src/cmd/mcp.go)
3. Model Context Protocol server is initialized for AI agent communication
4. WhatsApp client services are made available through MCP protocol
5. The MCP server listens for AI agent requests
