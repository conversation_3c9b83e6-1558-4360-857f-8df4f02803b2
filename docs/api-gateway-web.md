# API网关和Web界面实现

## 1. API网关实现

### 1.1 网关路由器

```go
// internal/gateway/router.go
package gateway

import (
    "fmt"
    "net/http"
    "net/http/httputil"
    "net/url"
    "strings"
    "sync"

    "github.com/gin-gonic/gin"
    "github.com/your-org/whatsapp-multi-container/internal/account"
    "github.com/your-org/whatsapp-multi-container/pkg/logger"
)

type Gateway struct {
    accountManager *account.Manager
    routes         map[string]*RouteInfo
    mu             sync.RWMutex
    log            logger.Logger
}

type RouteInfo struct {
    AccountID string
    Port      int
    Target    *url.URL
    Proxy     *httputil.ReverseProxy
}

func NewGateway(accountManager *account.Manager, log logger.Logger) *Gateway {
    return &Gateway{
        accountManager: accountManager,
        routes:         make(map[string]*RouteInfo),
        log:            log,
    }
}

func (g *Gateway) SetupRoutes(r *gin.Engine) {
    // 管理API路由
    api := r.Group("/api/v1")
    {
        // 账号管理
        accounts := api.Group("/accounts")
        {
            accounts.POST("", g.createAccount)
            accounts.GET("", g.listAccounts)
            accounts.GET("/:account_id", g.getAccount)
            accounts.DELETE("/:account_id", g.deleteAccount)
            accounts.POST("/:account_id/restart", g.restartAccount)
            accounts.GET("/:account_id/status", g.getAccountStatus)
        }

        // 代理管理
        proxies := api.Group("/proxies")
        {
            proxies.POST("", g.addProxy)
            proxies.GET("", g.listProxies)
            proxies.GET("/:proxy_id", g.getProxy)
            proxies.DELETE("/:proxy_id", g.removeProxy)
            proxies.POST("/:proxy_id/test", g.testProxy)
        }

        // 系统状态
        api.GET("/status", g.getSystemStatus)
        api.GET("/stats", g.getSystemStats)
    }

    // WhatsApp API代理路由
    whatsapp := r.Group("/whatsapp/:account_id")
    whatsapp.Use(g.accountMiddleware())
    {
        // 代理所有WhatsApp API请求
        whatsapp.Any("/*path", g.proxyToAccount)
    }

    // 静态文件和Web界面
    r.Static("/static", "./web/static")
    r.LoadHTMLGlob("web/templates/*")
    r.GET("/", g.webIndex)
    r.GET("/accounts", g.webAccounts)
    r.GET("/proxies", g.webProxies)
}

func (g *Gateway) accountMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        accountID := c.Param("account_id")
        if accountID == "" {
            c.JSON(http.StatusBadRequest, gin.H{"error": "account_id is required"})
            c.Abort()
            return
        }

        // 检查账号是否存在
        account, err := g.accountManager.GetAccount(accountID)
        if err != nil {
            c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("account not found: %s", accountID)})
            c.Abort()
            return
        }

        // 检查账号状态
        if account.Status != account.StatusRunning {
            c.JSON(http.StatusServiceUnavailable, gin.H{
                "error": fmt.Sprintf("account %s is not running, status: %s", accountID, account.Status),
            })
            c.Abort()
            return
        }

        c.Set("account", account)
        c.Next()
    }
}

func (g *Gateway) proxyToAccount(c *gin.Context) {
    account := c.MustGet("account").(*account.Account)
    path := c.Param("path")

    // 获取或创建代理
    routeInfo, err := g.getOrCreateRoute(account)
    if err != nil {
        g.log.Error("Failed to get route for account", "account_id", account.ID, "error", err)
        c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to route request"})
        return
    }

    // 修改请求路径
    c.Request.URL.Path = path
    c.Request.URL.RawPath = path

    // 代理请求
    routeInfo.Proxy.ServeHTTP(c.Writer, c.Request)
}

func (g *Gateway) getOrCreateRoute(account *account.Account) (*RouteInfo, error) {
    g.mu.RLock()
    if route, exists := g.routes[account.ID]; exists {
        g.mu.RUnlock()
        return route, nil
    }
    g.mu.RUnlock()

    g.mu.Lock()
    defer g.mu.Unlock()

    // 双重检查
    if route, exists := g.routes[account.ID]; exists {
        return route, nil
    }

    // 创建新的路由
    target, err := url.Parse(fmt.Sprintf("http://localhost:%d", account.Port))
    if err != nil {
        return nil, err
    }

    proxy := httputil.NewSingleHostReverseProxy(target)
    
    // 自定义错误处理
    proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
        g.log.Error("Proxy error", "account_id", account.ID, "error", err)
        w.WriteHeader(http.StatusBadGateway)
        w.Write([]byte(fmt.Sprintf("Service unavailable: %v", err)))
    }

    route := &RouteInfo{
        AccountID: account.ID,
        Port:      account.Port,
        Target:    target,
        Proxy:     proxy,
    }

    g.routes[account.ID] = route
    return route, nil
}

func (g *Gateway) removeRoute(accountID string) {
    g.mu.Lock()
    defer g.mu.Unlock()
    delete(g.routes, accountID)
}

// API处理器
func (g *Gateway) createAccount(c *gin.Context) {
    var req account.CreateRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    account, err := g.accountManager.CreateAccount(c.Request.Context(), &req)
    if err != nil {
        g.log.Error("Failed to create account", "error", err)
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusCreated, account)
}

func (g *Gateway) listAccounts(c *gin.Context) {
    accounts := g.accountManager.ListAccounts()
    c.JSON(http.StatusOK, gin.H{"accounts": accounts})
}

func (g *Gateway) getAccount(c *gin.Context) {
    accountID := c.Param("account_id")
    account, err := g.accountManager.GetAccount(accountID)
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, account)
}

func (g *Gateway) deleteAccount(c *gin.Context) {
    accountID := c.Param("account_id")
    
    if err := g.accountManager.DeleteAccount(c.Request.Context(), accountID); err != nil {
        g.log.Error("Failed to delete account", "account_id", accountID, "error", err)
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    // 清理路由
    g.removeRoute(accountID)

    c.JSON(http.StatusOK, gin.H{"message": "account deleted successfully"})
}

func (g *Gateway) restartAccount(c *gin.Context) {
    accountID := c.Param("account_id")
    
    if err := g.accountManager.RestartAccount(c.Request.Context(), accountID); err != nil {
        g.log.Error("Failed to restart account", "account_id", accountID, "error", err)
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    // 清理路由缓存，强制重新创建
    g.removeRoute(accountID)

    c.JSON(http.StatusOK, gin.H{"message": "account restarted successfully"})
}

func (g *Gateway) getAccountStatus(c *gin.Context) {
    accountID := c.Param("account_id")
    account, err := g.accountManager.GetAccount(accountID)
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
        return
    }

    // 获取容器详细状态
    status := gin.H{
        "account_id": account.ID,
        "status":     account.Status,
        "port":       account.Port,
        "proxy_id":   account.ProxyID,
        "last_seen":  account.LastSeen,
        "uptime":     time.Since(account.CreatedAt).String(),
    }

    c.JSON(http.StatusOK, status)
}

func (g *Gateway) getSystemStatus(c *gin.Context) {
    accounts := g.accountManager.ListAccounts()
    
    status := gin.H{
        "total_accounts": len(accounts),
        "running":        0,
        "stopped":        0,
        "failed":         0,
        "timestamp":      time.Now(),
    }

    for _, acc := range accounts {
        switch acc.Status {
        case account.StatusRunning:
            status["running"] = status["running"].(int) + 1
        case account.StatusStopped:
            status["stopped"] = status["stopped"].(int) + 1
        case account.StatusFailed:
            status["failed"] = status["failed"].(int) + 1
        }
    }

    c.JSON(http.StatusOK, status)
}

func (g *Gateway) getSystemStats(c *gin.Context) {
    // 实现系统统计信息
    stats := gin.H{
        "accounts": g.getAccountStats(),
        "proxies":  g.getProxyStats(),
        "system":   g.getSystemResourceStats(),
    }

    c.JSON(http.StatusOK, stats)
}

// Web界面处理器
func (g *Gateway) webIndex(c *gin.Context) {
    c.HTML(http.StatusOK, "index.html", gin.H{
        "title": "WhatsApp Multi-Container Manager",
    })
}

func (g *Gateway) webAccounts(c *gin.Context) {
    accounts := g.accountManager.ListAccounts()
    c.HTML(http.StatusOK, "accounts.html", gin.H{
        "title":    "Account Management",
        "accounts": accounts,
    })
}

func (g *Gateway) webProxies(c *gin.Context) {
    // 获取代理列表
    c.HTML(http.StatusOK, "proxies.html", gin.H{
        "title": "Proxy Management",
    })
}
```

### 1.2 中间件实现

```go
// internal/gateway/middleware.go
package gateway

import (
    "time"

    "github.com/gin-gonic/gin"
    "github.com/your-org/whatsapp-multi-container/pkg/logger"
)

// 日志中间件
func LoggerMiddleware(log logger.Logger) gin.HandlerFunc {
    return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
        log.Info("HTTP Request",
            "method", param.Method,
            "path", param.Path,
            "status", param.StatusCode,
            "latency", param.Latency,
            "client_ip", param.ClientIP,
            "user_agent", param.Request.UserAgent(),
        )
        return ""
    })
}

// CORS中间件
func CORSMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Header("Access-Control-Allow-Origin", "*")
        c.Header("Access-Control-Allow-Credentials", "true")
        c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
        c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }

        c.Next()
    }
}

// 限流中间件
func RateLimitMiddleware() gin.HandlerFunc {
    // 实现基于IP的限流
    return func(c *gin.Context) {
        // 简单实现，可以使用redis或内存存储
        c.Next()
    }
}

// 认证中间件
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 实现API密钥认证
        apiKey := c.GetHeader("X-API-Key")
        if apiKey == "" {
            c.JSON(401, gin.H{"error": "API key required"})
            c.Abort()
            return
        }

        // 验证API密钥
        if !validateAPIKey(apiKey) {
            c.JSON(401, gin.H{"error": "Invalid API key"})
            c.Abort()
            return
        }

        c.Next()
    }
}

func validateAPIKey(key string) bool {
    // 实现API密钥验证逻辑
    return true
}
```

## 2. Web界面实现

### 2.1 主页模板

```html
<!-- web/templates/index.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fab fa-whatsapp"></i> WhatsApp Manager
            </a>
            <div class="navbar-nav">
                <a class="nav-link" href="/accounts">账号管理</a>
                <a class="nav-link" href="/proxies">代理管理</a>
                <a class="nav-link" href="/monitor">监控面板</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-header">
                    <h1>系统概览</h1>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="total-accounts">0</h4>
                                            <p>总账号数</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="running-accounts">0</h4>
                                            <p>运行中</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-play-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="total-proxies">0</h4>
                                            <p>代理总数</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-network-wired fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="active-proxies">0</h4>
                                            <p>可用代理</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>最近活动</h5>
                            </div>
                            <div class="card-body">
                                <div id="recent-activities">
                                    <!-- 动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>系统状态</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="status-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
```

### 2.2 账号管理页面

```html
<!-- web/templates/accounts.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/accounts.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fab fa-whatsapp"></i> WhatsApp Manager
            </a>
            <div class="navbar-nav">
                <a class="nav-link active" href="/accounts">账号管理</a>
                <a class="nav-link" href="/proxies">代理管理</a>
                <a class="nav-link" href="/monitor">监控面板</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>账号管理</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAccountModal">
                        <i class="fas fa-plus"></i> 创建账号
                    </button>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="accounts-table">
                                <thead>
                                    <tr>
                                        <th>账号ID</th>
                                        <th>名称</th>
                                        <th>状态</th>
                                        <th>端口</th>
                                        <th>代理</th>
                                        <th>创建时间</th>
                                        <th>最后活跃</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {{range .accounts}}
                                    <tr data-account-id="{{.ID}}">
                                        <td>{{.ID}}</td>
                                        <td>{{.Name}}</td>
                                        <td>
                                            <span class="badge bg-{{if eq .Status "running"}}success{{else if eq .Status "stopped"}}warning{{else}}danger{{end}}">
                                                {{.Status}}
                                            </span>
                                        </td>
                                        <td>{{.Port}}</td>
                                        <td>{{.ProxyID}}</td>
                                        <td>{{.CreatedAt.Format "2006-01-02 15:04"}}</td>
                                        <td>{{.LastSeen.Format "2006-01-02 15:04"}}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewAccount('{{.ID}}')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="restartAccount('{{.ID}}')">
                                                    <i class="fas fa-redo"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteAccount('{{.ID}}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {{end}}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建账号模态框 -->
    <div class="modal fade" id="createAccountModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新账号</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="create-account-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account-id" class="form-label">账号ID</label>
                                    <input type="text" class="form-control" id="account-id" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account-name" class="form-label">账号名称</label>
                                    <input type="text" class="form-control" id="account-name" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="basic-auth" class="form-label">基础认证</label>
                                    <input type="text" class="form-control" id="basic-auth" placeholder="username:password">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="webhook-url" class="form-label">Webhook URL</label>
                                    <input type="url" class="form-control" id="webhook-url">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="auto-reply" class="form-label">自动回复消息</label>
                            <textarea class="form-control" id="auto-reply" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto-mark-read">
                                <label class="form-check-label" for="auto-mark-read">
                                    自动标记消息为已读
                                </label>
                            </div>
                        </div>

                        <h6>代理要求</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="proxy-country" class="form-label">国家</label>
                                    <select class="form-select" id="proxy-country">
                                        <option value="">任意</option>
                                        <option value="US">美国</option>
                                        <option value="UK">英国</option>
                                        <option value="DE">德国</option>
                                        <option value="JP">日本</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="proxy-type" class="form-label">代理类型</label>
                                    <select class="form-select" id="proxy-type">
                                        <option value="">任意</option>
                                        <option value="http">HTTP</option>
                                        <option value="socks5">SOCKS5</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="max-latency" class="form-label">最大延迟(秒)</label>
                                    <input type="number" class="form-control" id="max-latency" min="1" max="10" value="5">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createAccount()">创建账号</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/accounts.js"></script>
</body>
</html>
```

### 2.3 JavaScript实现

```javascript
// web/static/js/accounts.js
class AccountManager {
    constructor() {
        this.init();
    }

    init() {
        this.loadAccounts();
        this.setupEventListeners();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // 表单提交事件
        document.getElementById('create-account-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createAccount();
        });
    }

    async loadAccounts() {
        try {
            const response = await fetch('/api/v1/accounts');
            const data = await response.json();
            this.updateAccountsTable(data.accounts);
        } catch (error) {
            console.error('Failed to load accounts:', error);
            this.showAlert('加载账号列表失败', 'danger');
        }
    }

    updateAccountsTable(accounts) {
        const tbody = document.querySelector('#accounts-table tbody');
        tbody.innerHTML = '';

        accounts.forEach(account => {
            const row = this.createAccountRow(account);
            tbody.appendChild(row);
        });
    }

    createAccountRow(account) {
        const row = document.createElement('tr');
        row.setAttribute('data-account-id', account.id);
        
        const statusClass = this.getStatusClass(account.status);
        const lastSeen = new Date(account.last_seen).toLocaleString();
        const createdAt = new Date(account.created_at).toLocaleString();

        row.innerHTML = `
            <td>${account.id}</td>
            <td>${account.name}</td>
            <td><span class="badge bg-${statusClass}">${account.status}</span></td>
            <td>${account.port}</td>
            <td>${account.proxy_id}</td>
            <td>${createdAt}</td>
            <td>${lastSeen}</td>
            <td>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="accountManager.viewAccount('${account.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="accountManager.restartAccount('${account.id}')">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="accountManager.deleteAccount('${account.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        return row;
    }

    getStatusClass(status) {
        switch (status) {
            case 'running': return 'success';
            case 'stopped': return 'warning';
            case 'failed': return 'danger';
            default: return 'secondary';
        }
    }

    async createAccount() {
        const formData = this.getFormData();
        
        try {
            const response = await fetch('/api/v1/accounts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                this.showAlert('账号创建成功', 'success');
                this.closeModal('createAccountModal');
                this.loadAccounts();
            } else {
                const error = await response.json();
                this.showAlert(`创建失败: ${error.error}`, 'danger');
            }
        } catch (error) {
            console.error('Failed to create account:', error);
            this.showAlert('创建账号失败', 'danger');
        }
    }

    getFormData() {
        return {
            account_id: document.getElementById('account-id').value,
            name: document.getElementById('account-name').value,
            config: {
                basic_auth: document.getElementById('basic-auth').value,
                webhook_url: document.getElementById('webhook-url').value,
                auto_reply: document.getElementById('auto-reply').value,
                auto_mark_read: document.getElementById('auto-mark-read').checked,
            },
            proxy_requirements: {
                country: document.getElementById('proxy-country').value,
                proxy_type: document.getElementById('proxy-type').value,
                max_latency: document.getElementById('max-latency').value + 's',
                min_success_rate: 0.8
            }
        };
    }

    async deleteAccount(accountId) {
        if (!confirm(`确定要删除账号 ${accountId} 吗？此操作不可恢复。`)) {
            return;
        }

        try {
            const response = await fetch(`/api/v1/accounts/${accountId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showAlert('账号删除成功', 'success');
                this.loadAccounts();
            } else {
                const error = await response.json();
                this.showAlert(`删除失败: ${error.error}`, 'danger');
            }
        } catch (error) {
            console.error('Failed to delete account:', error);
            this.showAlert('删除账号失败', 'danger');
        }
    }

    async restartAccount(accountId) {
        try {
            const response = await fetch(`/api/v1/accounts/${accountId}/restart`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showAlert('账号重启成功', 'success');
                this.loadAccounts();
            } else {
                const error = await response.json();
                this.showAlert(`重启失败: ${error.error}`, 'danger');
            }
        } catch (error) {
            console.error('Failed to restart account:', error);
            this.showAlert('重启账号失败', 'danger');
        }
    }

    async viewAccount(accountId) {
        try {
            const response = await fetch(`/api/v1/accounts/${accountId}`);
            const account = await response.json();
            
            // 显示账号详情模态框
            this.showAccountDetails(account);
        } catch (error) {
            console.error('Failed to get account details:', error);
            this.showAlert('获取账号详情失败', 'danger');
        }
    }

    showAccountDetails(account) {
        // 实现账号详情显示
        console.log('Account details:', account);
    }

    showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild);

        // 自动消失
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    closeModal(modalId) {
        const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
        modal.hide();
    }

    startAutoRefresh() {
        // 每30秒自动刷新账号状态
        setInterval(() => {
            this.loadAccounts();
        }, 30000);
    }
}

// 初始化
const accountManager = new AccountManager();

// 全局函数，供HTML调用
function viewAccount(accountId) {
    accountManager.viewAccount(accountId);
}

function restartAccount(accountId) {
    accountManager.restartAccount(accountId);
}

function deleteAccount(accountId) {
    accountManager.deleteAccount(accountId);
}

function createAccount() {
    accountManager.createAccount();
}
```

这个实现提供了：

1. **完整的API网关**：路由管理、请求代理、中间件支持
2. **Web管理界面**：响应式设计、实时更新、用户友好
3. **账号管理功能**：创建、删除、重启、查看账号
4. **代理管理功能**：添加、测试、监控代理
5. **实时监控**：系统状态、账号状态、代理状态
6. **安全认证**：API密钥、CORS、限流
7. **错误处理**：友好的错误提示和处理

这些组件组合起来就是一个完整的多容器WhatsApp管理系统的前端和API层。
