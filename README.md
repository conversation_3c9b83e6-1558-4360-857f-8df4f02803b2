# 多容器WhatsApp管理系统

基于 `go-whatsapp-web-multidevice` 项目构建的企业级多容器管理系统，支持同时管理多个WhatsApp账号，每个账号运行在独立的容器中，配备独立的代理服务器。

## 🚀 核心特性

### 📱 多账号管理
- **独立容器**：每个WhatsApp账号运行在独立的Docker容器中
- **动态创建**：支持运行时动态创建和删除账号
- **状态监控**：实时监控每个账号的运行状态
- **自动恢复**：容器异常时自动重启和故障转移

### 🌐 智能代理池
- **代理分配**：智能分配代理服务器，避免IP封禁风险
- **健康检查**：自动检测代理连通性和性能
- **故障转移**：代理失效时自动切换到备用代理
- **负载均衡**：支持多种分配策略（轮询、最少使用、延迟优先等）

### 🔧 管理功能
- **Web界面**：直观的Web管理界面
- **API接口**：完整的RESTful API
- **批量操作**：支持批量管理多个账号
- **配置管理**：灵活的配置模板和环境变量

### 📊 监控告警
- **实时监控**：Prometheus + Grafana监控面板
- **性能指标**：CPU、内存、网络、消息统计
- **告警通知**：邮件、短信、Webhook通知
- **日志聚合**：结构化日志收集和分析

### 🔒 安全防护
- **数据隔离**：每个账号独立的数据存储
- **访问控制**：基于API密钥的认证
- **网络安全**：容器网络隔离和防火墙
- **数据加密**：敏感数据加密存储

## 📋 系统要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 100GB以上SSD
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Go**: 1.21+ (开发环境)

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web管理界面    │    │   API网关       │    │   监控系统      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    容器管理层                                      │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐ │
│  │   账号管理器     │    │   代理池管理     │    │   端口管理器     │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘ │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    容器实例层                                      │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐ │
│  │  WhatsApp-001   │    │  WhatsApp-002   │    │  WhatsApp-003   │ │
│  │  (Proxy-US-01)  │    │  (Proxy-EU-01)  │    │  (Proxy-AS-01)  │ │
│  │  Port: 3001     │    │  Port: 3002     │    │  Port: 3003     │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-org/whatsapp-multi-container.git
cd whatsapp-multi-container
```

### 2. 配置环境

```bash
# 复制配置文件
cp configs/config.example.yml configs/config.yml

# 编辑配置文件
vim configs/config.yml
```

### 3. 启动服务

```bash
# 开发环境
docker-compose -f deployments/compose/docker-compose.yml up -d

# 生产环境
docker-compose -f deployments/compose/docker-compose.prod.yml up -d
```

### 4. 访问管理界面

打开浏览器访问：http://localhost:8080

默认登录信息：
- 用户名：admin
- 密码：admin123

## 📖 文档目录

### 核心文档
- [系统设计文档](docs/multi-container-whatsapp-system.md) - 完整的系统架构和设计方案
- [代码实现文档](docs/code-implementation.md) - 核心代码实现和数据结构
- [API网关和Web界面](docs/api-gateway-web.md) - API网关和Web界面实现
- [部署配置文档](docs/deployment-configs.md) - 部署配置和运维脚本

### API文档
- [账号管理API](docs/api/accounts.md) - 账号创建、删除、管理接口
- [代理管理API](docs/api/proxies.md) - 代理池管理接口
- [监控API](docs/api/monitoring.md) - 系统监控和统计接口

### 运维文档
- [部署指南](docs/deployment/README.md) - 详细的部署步骤
- [监控配置](docs/monitoring/README.md) - Prometheus和Grafana配置
- [故障排除](docs/troubleshooting/README.md) - 常见问题和解决方案

## 🔧 配置说明

### 主配置文件

```yaml
# configs/config.yml
server:
  host: "0.0.0.0"
  port: 8080

database:
  driver: "sqlite3"
  dsn: "data/manager.db"

proxy_pool:
  check_interval: "5m"
  rotation_interval: "1h"
  allocation_strategy: "least_used"

container:
  image: "aldinokemal2104/go-whatsapp-web-multidevice"
  port_range:
    start: 3001
    end: 4000

logging:
  level: "info"
  format: "json"
```

### 代理配置

```yaml
# configs/proxies.yml
proxies:
  - id: "us-001"
    name: "US East 1"
    type: "http"
    host: "proxy1.example.com"
    port: 8080
    username: "user1"
    password: "pass1"
    country: "US"
    max_users: 1
```

## 📊 API接口

### 账号管理

```bash
# 创建账号
curl -X POST http://localhost:8080/api/v1/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "account_id": "test-001",
    "name": "测试账号1",
    "config": {
      "basic_auth": "admin:password",
      "webhook_url": "http://example.com/webhook"
    },
    "proxy_requirements": {
      "country": "US",
      "max_latency": "5s"
    }
  }'

# 获取账号列表
curl http://localhost:8080/api/v1/accounts

# 删除账号
curl -X DELETE http://localhost:8080/api/v1/accounts/test-001
```

### WhatsApp API代理

```bash
# 发送消息（通过代理）
curl -X POST http://localhost:8080/whatsapp/test-001/send/message \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "************",
    "message": "Hello from WhatsApp Manager!"
  }'

# 获取账号信息
curl http://localhost:8080/whatsapp/test-001/user/info
```

## 🔍 监控面板

### Grafana仪表板

访问 http://localhost:3000 查看监控面板：

- **系统概览**：账号状态、代理状态、系统资源
- **性能监控**：请求量、响应时间、错误率
- **容器监控**：CPU、内存、网络使用情况
- **代理监控**：代理延迟、成功率、使用情况

### 告警规则

- 账号异常下线
- 代理连接失败
- 系统资源使用率过高
- 消息发送失败率超阈值

## 🛠️ 开发指南

### 本地开发环境

```bash
# 安装依赖
go mod download

# 运行测试
go test ./...

# 启动开发服务器
go run cmd/manager/main.go

# 构建二进制文件
go build -o bin/manager cmd/manager/main.go
```

### 项目结构

```
whatsapp-multi-container/
├── cmd/                    # 主程序入口
├── internal/               # 内部包
│   ├── account/           # 账号管理
│   ├── proxy/             # 代理管理
│   ├── container/         # 容器管理
│   └── gateway/           # API网关
├── pkg/                   # 公共包
├── web/                   # Web界面
├── configs/               # 配置文件
├── docs/                  # 文档
└── deployments/           # 部署文件
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

- 本项目仅供学习和研究使用
- 请遵守WhatsApp的服务条款
- 使用代理服务器时请遵守当地法律法规
- 作者不对使用本项目造成的任何后果负责

## 🆘 支持

如果您遇到问题或需要帮助：

1. 查看 [故障排除文档](docs/troubleshooting/README.md)
2. 搜索 [Issues](https://github.com/your-org/whatsapp-multi-container/issues)
3. 创建新的 Issue
4. 联系维护者

## 🙏 致谢

- [go-whatsapp-web-multidevice](https://github.com/aldinokemal/go-whatsapp-web-multidevice) - 基础WhatsApp API
- [whatsmeow](https://github.com/tulir/whatsmeow) - WhatsApp Web协议库
- [Docker](https://www.docker.com/) - 容器化平台
- [Prometheus](https://prometheus.io/) - 监控系统
- [Grafana](https://grafana.com/) - 可视化面板

---

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**
