name: Build Docker Image

on:
  workflow_dispatch:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'
    branches-ignore:
      - '*'

jobs:
  build-and-push-amd:
    if: startsWith(github.ref, 'refs/tags/v') || github.event_name == 'workflow_dispatch'
    environment: production
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Login Registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          push: true
          context: .
          file: ./docker/golang.Dockerfile
          tags: ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:latest,${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:latest-amd, ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:${{ github.ref_name }}-amd

  build-and-push-arm:
    if: startsWith(github.ref, 'refs/tags/v') || github.event_name == 'workflow_dispatch'
    environment: production
    runs-on: [ARM64]
    steps:
      - uses: actions/checkout@v4
      - name: Login Registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          push: true
          context: .
          file: ./docker/golang.Dockerfile
          tags: ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:latest-arm, ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:${{ github.ref_name }}-arm

  merge-manifest:
    if: startsWith(github.ref, 'refs/tags/v') || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    needs: [build-and-push-amd, build-and-push-arm]
    steps:
      - name: Login Registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Create Versioned Manifest (${{ github.ref_name }})
        run: |
          docker buildx imagetools create -t ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:${{ github.ref_name }} \
            ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:${{ github.ref_name }}-amd \
            ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:${{ github.ref_name }}-arm
      - name: Create Latest Manifest
        run: |
          docker buildx imagetools create -t ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:latest \
            ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:latest-amd \
            ${{ secrets.REGISTRY_USERNAME }}/go-whatsapp-web-multidevice:latest-arm